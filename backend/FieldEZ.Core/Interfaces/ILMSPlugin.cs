using FieldEZ.Core.Models;

namespace FieldEZ.Core.Interfaces
{
    /// <summary>
    /// Base interface for all LMS plugins
    /// </summary>
    public interface ILMSPlugin
    {
        string Name { get; }
        string Version { get; }
        string Description { get; }
        LMSType Type { get; }
        bool IsEnabled { get; set; }
        
        Task<bool> TestConnectionAsync();
        Task InitializeAsync(Dictionary<string, string> configuration);
        Task<LMSHealthStatus> GetHealthStatusAsync();
    }

    /// <summary>
    /// Interface for data synchronization with LMS
    /// </summary>
    public interface ILMSDataSync : ILMSPlugin
    {
        Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null);
        Task<IEnumerable<LMSCustomer>> GetCustomersAsync(DateTime? lastSyncDate = null);
        Task<bool> UpdateLoanStatusAsync(string loanId, LoanStatus status);
        Task<bool> CreateCollectionRecordAsync(LMSCollectionRecord record);
        Task<bool> UpdatePaymentStatusAsync(string paymentId, PaymentStatus status);
        Task<SyncResult> SyncDataAsync(SyncRequest request);
    }

    /// <summary>
    /// Interface for real-time notifications from LMS
    /// </summary>
    public interface ILMSNotificationHandler : ILMSPlugin
    {
        event EventHandler<LMSNotificationEventArgs> NotificationReceived;
        Task StartListeningAsync();
        Task StopListeningAsync();
        Task<bool> SubscribeToEventsAsync(IEnumerable<LMSEventType> eventTypes);
    }

    /// <summary>
    /// Interface for webhook handling from LMS
    /// </summary>
    public interface ILMSWebhookHandler : ILMSPlugin
    {
        Task<WebhookResponse> HandleWebhookAsync(WebhookRequest request);
        Task<bool> RegisterWebhookAsync(string callbackUrl, IEnumerable<LMSEventType> eventTypes);
        Task<bool> UnregisterWebhookAsync(string webhookId);
        Task<IEnumerable<WebhookRegistration>> GetActiveWebhooksAsync();
    }

    /// <summary>
    /// Interface for reporting and analytics integration
    /// </summary>
    public interface ILMSReporting : ILMSPlugin
    {
        Task<LMSReport> GenerateReportAsync(ReportRequest request);
        Task<IEnumerable<LMSMetric>> GetMetricsAsync(MetricsRequest request);
        Task<bool> SendCollectionReportAsync(CollectionReport report);
        Task<DashboardData> GetDashboardDataAsync(string agentId, DateTime date);
    }

    /// <summary>
    /// Interface for document management integration
    /// </summary>
    public interface ILMSDocumentManager : ILMSPlugin
    {
        Task<string> UploadDocumentAsync(DocumentUploadRequest request);
        Task<Stream> DownloadDocumentAsync(string documentId);
        Task<bool> DeleteDocumentAsync(string documentId);
        Task<IEnumerable<LMSDocument>> GetDocumentsAsync(string entityId, DocumentType type);
        Task<bool> LinkDocumentToLoanAsync(string documentId, string loanId);
    }

    /// <summary>
    /// Enums and supporting types
    /// </summary>
    public enum LMSType
    {
        Finacle,
        Temenos,
        Nucleus,
        FinnOne,
        Custom,
        CloudBased,
        OnPremise
    }

    public enum LMSEventType
    {
        LoanCreated,
        LoanUpdated,
        PaymentReceived,
        PaymentFailed,
        CustomerUpdated,
        CollectionAssigned,
        StatusChanged
    }

    public enum DocumentType
    {
        LoanAgreement,
        PaymentReceipt,
        CollectionNotice,
        CustomerDocument,
        VisitReport,
        Other
    }

    /// <summary>
    /// Data transfer objects for LMS integration
    /// </summary>
    public class LMSLoan
    {
        public string LoanId { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal PrincipalAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int OverdueDays { get; set; }
        public decimal InterestRate { get; set; }
        public decimal EMIAmount { get; set; }
        public LoanStatus Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class LMSCustomer
    {
        public string CustomerId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Pincode { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class LMSCollectionRecord
    {
        public string LoanId { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public PaymentMethod Method { get; set; }
        public DateTime CollectionDate { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class SyncRequest
    {
        public DateTime? LastSyncDate { get; set; }
        public IEnumerable<string> EntityTypes { get; set; } = new List<string>();
        public Dictionary<string, object> Filters { get; set; } = new();
        public int BatchSize { get; set; } = 100;
        public bool FullSync { get; set; } = false;
    }

    public class SyncResult
    {
        public bool Success { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsCreated { get; set; }
        public int RecordsSkipped { get; set; }
        public DateTime SyncTimestamp { get; set; }
        public IEnumerable<string> Errors { get; set; } = new List<string>();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class LMSHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
    }

    public class LMSNotificationEventArgs : EventArgs
    {
        public LMSEventType EventType { get; set; }
        public string EntityId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class WebhookRequest
    {
        public string WebhookId { get; set; } = string.Empty;
        public LMSEventType EventType { get; set; }
        public string Payload { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public DateTime Timestamp { get; set; }
    }

    public class WebhookResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int StatusCode { get; set; } = 200;
    }

    public class WebhookRegistration
    {
        public string WebhookId { get; set; } = string.Empty;
        public string CallbackUrl { get; set; } = string.Empty;
        public IEnumerable<LMSEventType> EventTypes { get; set; } = new List<LMSEventType>();
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    // Additional DTOs for reporting and document management
    public class ReportRequest
    {
        public string ReportType { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class LMSReport
    {
        public string ReportId { get; set; } = string.Empty;
        public string ReportType { get; set; } = string.Empty;
        public DateTime GeneratedDate { get; set; }
        public string Format { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class MetricsRequest
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public IEnumerable<string> MetricTypes { get; set; } = new List<string>();
        public string? AgentId { get; set; }
    }

    public class LMSMetric
    {
        public string MetricName { get; set; } = string.Empty;
        public object Value { get; set; } = new();
        public string Unit { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class CollectionReport
    {
        public string AgentId { get; set; } = string.Empty;
        public DateTime ReportDate { get; set; }
        public decimal TotalCollected { get; set; }
        public int VisitsCompleted { get; set; }
        public IEnumerable<LMSCollectionRecord> Collections { get; set; } = new List<LMSCollectionRecord>();
    }

    public class DocumentUploadRequest
    {
        public Stream FileStream { get; set; } = Stream.Null;
        public string FileName { get; set; } = string.Empty;
        public DocumentType DocumentType { get; set; }
        public string EntityId { get; set; } = string.Empty;
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    public class LMSDocument
    {
        public string DocumentId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public DocumentType DocumentType { get; set; }
        public string EntityId { get; set; } = string.Empty;
        public DateTime UploadDate { get; set; }
        public long FileSize { get; set; }
        public string DownloadUrl { get; set; } = string.Empty;
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    public class DashboardData
    {
        public Dictionary<string, object> Metrics { get; set; } = new();
        public IEnumerable<LMSLoan> RecentLoans { get; set; } = new List<LMSLoan>();
        public IEnumerable<LMSCollectionRecord> RecentCollections { get; set; } = new List<LMSCollectionRecord>();
    }
}
