using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using NetTopologySuite.Geometries;

namespace FieldEZ.Core.Models
{
    public class Visit
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid LeadId { get; set; }

        [Required]
        public Guid AgentId { get; set; }

        // Visit details
        [Required]
        public DateTime VisitDate { get; set; }

        public Point? Location { get; set; }

        [Required]
        public VisitResult Result { get; set; }

        public string? Notes { get; set; }

        // Duration tracking
        public DateTime? CheckInTime { get; set; }

        public DateTime? CheckOutTime { get; set; }

        public int? DurationMinutes { get; set; }

        // Follow-up
        public DateTime? NextVisitScheduled { get; set; }

        public string? FollowUpNotes { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Lead Lead { get; set; } = null!;
        public virtual Agent Agent { get; set; } = null!;
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }

    public enum VisitResult
    {
        PaymentCollected,
        PartialPayment,
        CustomerNotAvailable,
        RefusedToPay,
        Rescheduled
    }
}

namespace FieldEZ.Core.Models
{
    public class Payment
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid LeadId { get; set; }

        public Guid? VisitId { get; set; }

        [Required]
        public Guid AgentId { get; set; }

        // Payment details
        [Required]
        [Column(TypeName = "decimal(15,2)")]
        public decimal Amount { get; set; }

        [Required]
        public PaymentMethod Method { get; set; }

        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        // Transaction details
        [StringLength(255)]
        public string? TransactionId { get; set; }

        [StringLength(100)]
        public string? GatewayName { get; set; }

        public string? GatewayResponse { get; set; } // JSON response

        // UPI specific
        [StringLength(255)]
        public string? UPIId { get; set; }

        [StringLength(255)]
        public string? UPIRefId { get; set; }

        // Card specific
        [StringLength(4)]
        public string? CardLastFour { get; set; }

        [StringLength(50)]
        public string? CardType { get; set; }

        // Receipt information
        [StringLength(100)]
        public string? ReceiptNumber { get; set; }

        [StringLength(500)]
        public string? ReceiptUrl { get; set; }

        // Location and timing
        public Point? PaymentLocation { get; set; }

        [Required]
        public DateTime PaymentTimestamp { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Lead Lead { get; set; } = null!;
        public virtual Visit? Visit { get; set; }
        public virtual Agent Agent { get; set; } = null!;
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }

    public enum PaymentMethod
    {
        Cash,
        UPI,
        Card,
        BankTransfer,
        Cheque
    }

    public enum PaymentStatus
    {
        Pending,
        Success,
        Failed,
        Cancelled
    }
}

namespace FieldEZ.Core.Models
{
    public class Document
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        // Relationships
        public Guid? LeadId { get; set; }
        public Guid? VisitId { get; set; }
        public Guid? PaymentId { get; set; }

        [Required]
        public Guid AgentId { get; set; }

        // Document details
        [Required]
        public DocumentType DocumentType { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FileUrl { get; set; } = string.Empty;

        public long? FileSize { get; set; }

        [StringLength(100)]
        public string? MimeType { get; set; }

        // Metadata
        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Lead? Lead { get; set; }
        public virtual Visit? Visit { get; set; }
        public virtual Payment? Payment { get; set; }
        public virtual Agent Agent { get; set; } = null!;
    }

    public enum DocumentType
    {
        Receipt,
        Photo,
        Signature,
        Agreement,
        Other
    }
}

namespace FieldEZ.Core.Models
{
    public class AgentLocation
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid AgentId { get; set; }

        [Required]
        public Point Location { get; set; } = null!;

        [Column(TypeName = "decimal(8,2)")]
        public decimal? Accuracy { get; set; } // GPS accuracy in meters

        [Required]
        public DateTime Timestamp { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Agent Agent { get; set; } = null!;
    }
}

namespace FieldEZ.Core.Models
{
    public class Notification
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        public Guid? AgentId { get; set; } // NULL for broadcast notifications

        // Notification content
        [Required]
        [StringLength(255)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Message { get; set; } = string.Empty;

        public NotificationType Type { get; set; } = NotificationType.Info;

        public NotificationPriority Priority { get; set; } = NotificationPriority.Medium;

        // Delivery tracking
        public DateTime SentAt { get; set; } = DateTime.UtcNow;

        public DateTime? ReadAt { get; set; }

        public bool IsRead { get; set; } = false;

        // Action data
        [StringLength(500)]
        public string? ActionUrl { get; set; }

        public string? ActionData { get; set; } // JSON data

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Agent? Agent { get; set; }
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,
        Success
    }

    public enum NotificationPriority
    {
        Low,
        Medium,
        High
    }
}

namespace FieldEZ.Core.Models
{
    public class SyncLog
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid AgentId { get; set; }

        [Required]
        [StringLength(255)]
        public string DeviceId { get; set; } = string.Empty;

        // Sync details
        [Required]
        public SyncType SyncType { get; set; }

        [Required]
        public SyncStatus Status { get; set; }

        // Data counts
        public int RecordsUploaded { get; set; } = 0;
        public int RecordsDownloaded { get; set; } = 0;
        public int ConflictsResolved { get; set; } = 0;

        // Timing
        [Required]
        public DateTime StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public int? DurationSeconds { get; set; }

        // Error handling
        public string? ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Agent Agent { get; set; } = null!;
    }

    public enum SyncType
    {
        Upload,
        Download,
        FullSync
    }

    public enum SyncStatus
    {
        Pending,
        Success,
        Failed,
        Partial
    }
}
