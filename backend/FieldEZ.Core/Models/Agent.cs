using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace FieldEZ.Core.Models
{
    public class Agent
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [StringLength(50)]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        public AgentStatus Status { get; set; } = AgentStatus.Active;

        [StringLength(50)]
        public string Role { get; set; } = "FieldAgent";

        // Location data using NetTopologySuite for spatial data
        public Point? CurrentLocation { get; set; }
        public Polygon? TerritoryBounds { get; set; }
        public Point? BaseLocation { get; set; }

        // Performance metrics
        public int TotalCollections { get; set; } = 0;

        [Column(TypeName = "decimal(15,2)")]
        public decimal TotalAmountCollected { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal SuccessRate { get; set; } = 0;

        public int AvgCollectionTimeMinutes { get; set; } = 0;

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastActiveAt { get; set; }

        // Navigation properties
        public virtual ICollection<Lead> AssignedLeads { get; set; } = new List<Lead>();
        public virtual ICollection<Visit> Visits { get; set; } = new List<Visit>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<AgentLocation> LocationHistory { get; set; } = new List<AgentLocation>();
        public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<SyncLog> SyncLogs { get; set; } = new List<SyncLog>();
    }

    public enum AgentStatus
    {
        Active,
        Inactive,
        Suspended
    }
}

namespace FieldEZ.Core.Models
{
    public class Customer
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        // Address information
        [StringLength(255)]
        public string? AddressLine1 { get; set; }

        [StringLength(255)]
        public string? AddressLine2 { get; set; }

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(100)]
        public string? State { get; set; }

        [StringLength(10)]
        public string? Pincode { get; set; }

        [StringLength(100)]
        public string Country { get; set; } = "India";

        public Point? Coordinates { get; set; }

        // Additional details
        public DateTime? DateOfBirth { get; set; }

        [StringLength(100)]
        public string? Occupation { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal? AnnualIncome { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<Loan> Loans { get; set; } = new List<Loan>();
        public virtual ICollection<Lead> Leads { get; set; } = new List<Lead>();
    }
}

namespace FieldEZ.Core.Models
{
    public class Loan
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [StringLength(50)]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        public Guid CustomerId { get; set; }

        // Loan details
        [Required]
        [Column(TypeName = "decimal(15,2)")]
        public decimal PrincipalAmount { get; set; }

        [Required]
        [Column(TypeName = "decimal(5,2)")]
        public decimal InterestRate { get; set; }

        [Required]
        public int TenureMonths { get; set; }

        [Required]
        [Column(TypeName = "decimal(15,2)")]
        public decimal EMIAmount { get; set; }

        // Current status
        [Required]
        [Column(TypeName = "decimal(15,2)")]
        public decimal OutstandingAmount { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal OverdueAmount { get; set; } = 0;

        public DateTime? LastPaymentDate { get; set; }

        [Required]
        public DateTime NextDueDate { get; set; }

        public int OverdueDays { get; set; } = 0;

        // Loan lifecycle
        [Required]
        public DateTime DisbursedAt { get; set; }

        [Required]
        public DateTime MaturityDate { get; set; }

        public DateTime? ClosedAt { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual ICollection<Lead> Leads { get; set; } = new List<Lead>();
    }
}

namespace FieldEZ.Core.Models
{
    public class Lead
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid LoanId { get; set; }

        [Required]
        public Guid CustomerId { get; set; }

        public Guid? AssignedAgentId { get; set; }

        // Assignment details
        public DateTime? AssignedAt { get; set; }

        public LeadPriority Priority { get; set; } = LeadPriority.Medium;

        [Column(TypeName = "decimal(15,2)")]
        public decimal? ExpectedCollectionAmount { get; set; }

        public DateTime? Deadline { get; set; }

        // Status tracking
        public LeadStatus Status { get; set; } = LeadStatus.Assigned;

        public DateTime? LastVisitDate { get; set; }

        public int VisitCount { get; set; } = 0;

        public int SuccessfulVisits { get; set; } = 0;

        // Collection progress
        [Column(TypeName = "decimal(15,2)")]
        public decimal TotalCollected { get; set; } = 0;

        public DateTime? LastCollectionDate { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Loan Loan { get; set; } = null!;
        public virtual Customer Customer { get; set; } = null!;
        public virtual Agent? AssignedAgent { get; set; }
        public virtual ICollection<Visit> Visits { get; set; } = new List<Visit>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }

    public enum LeadStatus
    {
        Assigned,
        Visited,
        Collected,
        Failed,
        Escalated
    }

    public enum LeadPriority
    {
        Low,
        Medium,
        High,
        Urgent
    }

    public enum LoanStatus
    {
        Active,
        Closed,
        Defaulted,
        Suspended,
        PartiallyPaid
    }
}
