using Microsoft.Extensions.Logging;
using FieldEZ.Core.Interfaces;
using FieldEZ.Core.Models;
using System.Text.Json;
using System.Net.Http;
using System.Text;

namespace FieldEZ.Infrastructure.LMS.Plugins
{
    [AutoLoadPlugin(false)] // Manual loading for Finacle
    public class FinaclePlugin : ILMSDataSync, ILMSWebhookHandler, ILMSReporting
    {
        private readonly ILogger<FinaclePlugin> _logger;
        private readonly HttpClient _httpClient;
        private Dictionary<string, string> _configuration = new();
        private string _baseUrl = string.Empty;
        private string _apiKey = string.Empty;
        private string _username = string.Empty;
        private string _password = string.Empty;

        public string Name => "Finacle LMS Plugin";
        public string Version => "1.0.0";
        public string Description => "Integration plugin for Infosys Finacle Core Banking System";
        public LMSType Type => LMSType.Finacle;
        public bool IsEnabled { get; set; } = true;

        public FinaclePlugin(ILogger<FinaclePlugin> logger, HttpClient httpClient)
        {
            _logger = logger;
            _httpClient = httpClient;
        }

        public async Task InitializeAsync(Dictionary<string, string> configuration)
        {
            _configuration = configuration;
            _baseUrl = configuration.GetValueOrDefault("BaseUrl", "");
            _apiKey = configuration.GetValueOrDefault("ApiKey", "");
            _username = configuration.GetValueOrDefault("Username", "");
            _password = configuration.GetValueOrDefault("Password", "");

            // Configure HTTP client
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            
            // Set authentication header
            var authToken = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_username}:{_password}"));
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {authToken}");

            _logger.LogInformation("Finacle plugin initialized with base URL: {BaseUrl}", _baseUrl);
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/health");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Finacle connection test failed");
                return false;
            }
        }

        public async Task<LMSHealthStatus> GetHealthStatusAsync()
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/health");
                var responseTime = DateTime.UtcNow - startTime;

                return new LMSHealthStatus
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    Status = response.IsSuccessStatusCode ? "Healthy" : $"Error: {response.StatusCode}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = responseTime,
                    Details = new Dictionary<string, object>
                    {
                        ["StatusCode"] = (int)response.StatusCode,
                        ["BaseUrl"] = _baseUrl
                    }
                };
            }
            catch (Exception ex)
            {
                return new LMSHealthStatus
                {
                    IsHealthy = false,
                    Status = $"Error: {ex.Message}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
        }

        #region ILMSDataSync Implementation

        public async Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                var url = "/api/v1/loans";
                if (lastSyncDate.HasValue)
                {
                    url += $"?lastModified={lastSyncDate.Value:yyyy-MM-ddTHH:mm:ssZ}";
                }

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var finacleLoans = JsonSerializer.Deserialize<FinacleLoanResponse>(content);

                return finacleLoans?.Loans?.Select(MapToLMSLoan) ?? Enumerable.Empty<LMSLoan>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get loans from Finacle");
                throw;
            }
        }

        public async Task<IEnumerable<LMSCustomer>> GetCustomersAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                var url = "/api/v1/customers";
                if (lastSyncDate.HasValue)
                {
                    url += $"?lastModified={lastSyncDate.Value:yyyy-MM-ddTHH:mm:ssZ}";
                }

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var finacleCustomers = JsonSerializer.Deserialize<FinacleCustomerResponse>(content);

                return finacleCustomers?.Customers?.Select(MapToLMSCustomer) ?? Enumerable.Empty<LMSCustomer>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get customers from Finacle");
                throw;
            }
        }

        public async Task<bool> UpdateLoanStatusAsync(string loanId, LoanStatus status)
        {
            try
            {
                var request = new
                {
                    LoanId = loanId,
                    Status = status.ToString(),
                    UpdatedBy = "FieldEZ",
                    UpdatedDate = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"/api/v1/loans/{loanId}/status", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update loan status in Finacle for loan: {LoanId}", loanId);
                return false;
            }
        }

        public async Task<bool> CreateCollectionRecordAsync(LMSCollectionRecord record)
        {
            try
            {
                var finacleRecord = new
                {
                    AccountNumber = record.LoanId,
                    CollectorId = record.AgentId,
                    Amount = record.Amount,
                    PaymentMode = MapPaymentMethod(record.Method),
                    CollectionDate = record.CollectionDate,
                    TransactionReference = record.TransactionId,
                    ReceiptNumber = record.ReceiptNumber,
                    Remarks = record.Notes,
                    CustomFields = record.CustomFields
                };

                var json = JsonSerializer.Serialize(finacleRecord);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/v1/collections", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create collection record in Finacle");
                return false;
            }
        }

        public async Task<bool> UpdatePaymentStatusAsync(string paymentId, PaymentStatus status)
        {
            try
            {
                var request = new
                {
                    PaymentId = paymentId,
                    Status = status.ToString(),
                    UpdatedDate = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"/api/v1/payments/{paymentId}/status", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update payment status in Finacle");
                return false;
            }
        }

        public async Task<SyncResult> SyncDataAsync(SyncRequest request)
        {
            var result = new SyncResult
            {
                SyncTimestamp = DateTime.UtcNow,
                Success = true,
                Errors = new List<string>()
            };

            try
            {
                // Sync loans
                if (request.EntityTypes.Contains("loans") || !request.EntityTypes.Any())
                {
                    var loans = await GetLoansAsync(request.LastSyncDate);
                    result.RecordsProcessed += loans.Count();
                    // Process loans here
                }

                // Sync customers
                if (request.EntityTypes.Contains("customers") || !request.EntityTypes.Any())
                {
                    var customers = await GetCustomersAsync(request.LastSyncDate);
                    result.RecordsProcessed += customers.Count();
                    // Process customers here
                }

                _logger.LogInformation("Finacle sync completed. Processed {Count} records", result.RecordsProcessed);
            }
            catch (Exception ex)
            {
                result.Success = false;
                ((List<string>)result.Errors).Add(ex.Message);
                _logger.LogError(ex, "Finacle sync failed");
            }

            return result;
        }

        #endregion

        #region ILMSWebhookHandler Implementation

        public async Task<WebhookResponse> HandleWebhookAsync(WebhookRequest request)
        {
            try
            {
                _logger.LogInformation("Processing Finacle webhook: {EventType}", request.EventType);

                var payload = JsonSerializer.Deserialize<FinacleWebhookPayload>(request.Payload);
                
                // Process webhook based on event type
                switch (request.EventType)
                {
                    case LMSEventType.LoanCreated:
                    case LMSEventType.LoanUpdated:
                        await ProcessLoanWebhook(payload);
                        break;
                    case LMSEventType.PaymentReceived:
                        await ProcessPaymentWebhook(payload);
                        break;
                    default:
                        _logger.LogWarning("Unhandled webhook event type: {EventType}", request.EventType);
                        break;
                }

                return new WebhookResponse
                {
                    Success = true,
                    Message = "Webhook processed successfully",
                    StatusCode = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process Finacle webhook");
                return new WebhookResponse
                {
                    Success = false,
                    Message = ex.Message,
                    StatusCode = 500
                };
            }
        }

        public async Task<bool> RegisterWebhookAsync(string callbackUrl, IEnumerable<LMSEventType> eventTypes)
        {
            try
            {
                var request = new
                {
                    CallbackUrl = callbackUrl,
                    EventTypes = eventTypes.Select(e => e.ToString()),
                    Active = true
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/v1/webhooks", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register webhook with Finacle");
                return false;
            }
        }

        public async Task<bool> UnregisterWebhookAsync(string webhookId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/webhooks/{webhookId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unregister webhook from Finacle");
                return false;
            }
        }

        public async Task<IEnumerable<WebhookRegistration>> GetActiveWebhooksAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/webhooks");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var webhooks = JsonSerializer.Deserialize<FinacleWebhookResponse>(content);

                return webhooks?.Webhooks?.Select(w => new WebhookRegistration
                {
                    WebhookId = w.Id,
                    CallbackUrl = w.CallbackUrl,
                    EventTypes = w.EventTypes.Select(e => Enum.Parse<LMSEventType>(e)),
                    IsActive = w.Active,
                    CreatedDate = w.CreatedDate
                }) ?? Enumerable.Empty<WebhookRegistration>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get active webhooks from Finacle");
                return Enumerable.Empty<WebhookRegistration>();
            }
        }

        #endregion

        #region ILMSReporting Implementation

        public async Task<LMSReport> GenerateReportAsync(ReportRequest request)
        {
            // Implementation for generating reports from Finacle
            throw new NotImplementedException("Finacle reporting not yet implemented");
        }

        public async Task<IEnumerable<LMSMetric>> GetMetricsAsync(MetricsRequest request)
        {
            // Implementation for getting metrics from Finacle
            throw new NotImplementedException("Finacle metrics not yet implemented");
        }

        public async Task<bool> SendCollectionReportAsync(CollectionReport report)
        {
            // Implementation for sending collection reports to Finacle
            throw new NotImplementedException("Finacle collection reporting not yet implemented");
        }

        public async Task<DashboardData> GetDashboardDataAsync(string agentId, DateTime date)
        {
            // Implementation for getting dashboard data from Finacle
            throw new NotImplementedException("Finacle dashboard data not yet implemented");
        }

        #endregion

        #region Helper Methods

        private LMSLoan MapToLMSLoan(FinacleLoan finacleloan)
        {
            return new LMSLoan
            {
                LoanId = finacleloan.AccountNumber,
                AccountNumber = finacleloan.AccountNumber,
                CustomerId = finacleloan.CustomerId,
                PrincipalAmount = finacleloan.PrincipalAmount,
                OutstandingAmount = finacleloan.OutstandingBalance,
                OverdueAmount = finacleloan.OverdueAmount,
                NextDueDate = finacleloan.NextDueDate,
                OverdueDays = finacleloan.OverdueDays,
                InterestRate = finacleloan.InterestRate,
                EMIAmount = finacleloan.EMIAmount,
                Status = Enum.Parse<LoanStatus>(finacleloan.Status),
                CreatedDate = finacleloan.CreatedDate,
                LastUpdated = finacleloan.LastModified,
                CustomFields = finacleloan.CustomFields ?? new Dictionary<string, object>()
            };
        }

        private LMSCustomer MapToLMSCustomer(FinacleCustomer finacleCustomer)
        {
            return new LMSCustomer
            {
                CustomerId = finacleCustomer.CustomerId,
                Name = finacleCustomer.Name,
                Phone = finacleCustomer.MobileNumber,
                Email = finacleCustomer.EmailAddress,
                Address = finacleCustomer.Address,
                City = finacleCustomer.City,
                State = finacleCustomer.State,
                Pincode = finacleCustomer.PinCode,
                CreatedDate = finacleCustomer.CreatedDate,
                LastUpdated = finacleCustomer.LastModified,
                CustomFields = finacleCustomer.CustomFields ?? new Dictionary<string, object>()
            };
        }

        private string MapPaymentMethod(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Cash => "CASH",
                PaymentMethod.UPI => "UPI",
                PaymentMethod.Card => "CARD",
                PaymentMethod.BankTransfer => "NEFT",
                PaymentMethod.Cheque => "CHEQUE",
                _ => "OTHER"
            };
        }

        private async Task ProcessLoanWebhook(FinacleWebhookPayload payload)
        {
            // Process loan-related webhook
            _logger.LogInformation("Processing loan webhook for account: {AccountNumber}", payload.AccountNumber);
        }

        private async Task ProcessPaymentWebhook(FinacleWebhookPayload payload)
        {
            // Process payment-related webhook
            _logger.LogInformation("Processing payment webhook for account: {AccountNumber}", payload.AccountNumber);
        }

        #endregion
    }

    #region Finacle Data Models

    public class FinacleLoanResponse
    {
        public List<FinacleLoan> Loans { get; set; } = new();
    }

    public class FinacleLoan
    {
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal PrincipalAmount { get; set; }
        public decimal OutstandingBalance { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int OverdueDays { get; set; }
        public decimal InterestRate { get; set; }
        public decimal EMIAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
    }

    public class FinacleCustomerResponse
    {
        public List<FinacleCustomer> Customers { get; set; } = new();
    }

    public class FinacleCustomer
    {
        public string CustomerId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PinCode { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
    }

    public class FinacleWebhookPayload
    {
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string EventType { get; set; } = string.Empty;
        public DateTime EventDate { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class FinacleWebhookResponse
    {
        public List<FinacleWebhook> Webhooks { get; set; } = new();
    }

    public class FinacleWebhook
    {
        public string Id { get; set; } = string.Empty;
        public string CallbackUrl { get; set; } = string.Empty;
        public List<string> EventTypes { get; set; } = new();
        public bool Active { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    #endregion
}
