using Microsoft.Extensions.Logging;
using FieldEZ.Core.Interfaces;
using FieldEZ.Core.Models;
using System.Text.Json;
using System.Net.Http;
using System.Text;
using System.Xml.Linq;

namespace FieldEZ.Infrastructure.LMS.Plugins
{
    [AutoLoadPlugin(false)]
    public class TemenosPlugin : ILMSDataSync, ILMSNotificationHandler, ILMSDocumentManager
    {
        private readonly ILogger<TemenosPlugin> _logger;
        private readonly HttpClient _httpClient;
        private Dictionary<string, string> _configuration = new();
        private string _baseUrl = string.Empty;
        private string _clientId = string.Empty;
        private string _clientSecret = string.Empty;
        private string _accessToken = string.Empty;
        private Timer? _tokenRefreshTimer;

        public string Name => "Temenos T24 Plugin";
        public string Version => "1.0.0";
        public string Description => "Integration plugin for Temenos T24 Core Banking Platform";
        public LMSType Type => LMSType.Temenos;
        public bool IsEnabled { get; set; } = true;

        public event EventHandler<LMSNotificationEventArgs>? NotificationReceived;

        public TemenosPlugin(ILogger<TemenosPlugin> logger, HttpClient httpClient)
        {
            _logger = logger;
            _httpClient = httpClient;
        }

        public async Task InitializeAsync(Dictionary<string, string> configuration)
        {
            _configuration = configuration;
            _baseUrl = configuration.GetValueOrDefault("BaseUrl", "");
            _clientId = configuration.GetValueOrDefault("ClientId", "");
            _clientSecret = configuration.GetValueOrDefault("ClientSecret", "");

            _httpClient.BaseAddress = new Uri(_baseUrl);

            // Get initial access token
            await RefreshAccessTokenAsync();

            // Set up token refresh timer (refresh every 50 minutes for 1-hour tokens)
            _tokenRefreshTimer = new Timer(async _ => await RefreshAccessTokenAsync(), 
                null, TimeSpan.FromMinutes(50), TimeSpan.FromMinutes(50));

            _logger.LogInformation("Temenos plugin initialized with base URL: {BaseUrl}", _baseUrl);
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1.0.0/system/health");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Temenos connection test failed");
                return false;
            }
        }

        public async Task<LMSHealthStatus> GetHealthStatusAsync()
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var response = await _httpClient.GetAsync("/api/v1.0.0/system/health");
                var responseTime = DateTime.UtcNow - startTime;

                return new LMSHealthStatus
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    Status = response.IsSuccessStatusCode ? "Healthy" : $"Error: {response.StatusCode}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = responseTime,
                    Details = new Dictionary<string, object>
                    {
                        ["StatusCode"] = (int)response.StatusCode,
                        ["BaseUrl"] = _baseUrl,
                        ["HasValidToken"] = !string.IsNullOrEmpty(_accessToken)
                    }
                };
            }
            catch (Exception ex)
            {
                return new LMSHealthStatus
                {
                    IsHealthy = false,
                    Status = $"Error: {ex.Message}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
        }

        #region ILMSDataSync Implementation

        public async Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                await EnsureValidTokenAsync();

                var url = "/api/v1.0.0/holdings/arrangements/loans";
                if (lastSyncDate.HasValue)
                {
                    url += $"?lastModified={lastSyncDate.Value:yyyy-MM-dd}";
                }

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var temenosResponse = JsonSerializer.Deserialize<TemenosLoanResponse>(content);

                return temenosResponse?.Body?.Select(MapToLMSLoan) ?? Enumerable.Empty<LMSLoan>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get loans from Temenos");
                throw;
            }
        }

        public async Task<IEnumerable<LMSCustomer>> GetCustomersAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                await EnsureValidTokenAsync();

                var url = "/api/v1.0.0/party/customers";
                if (lastSyncDate.HasValue)
                {
                    url += $"?lastModified={lastSyncDate.Value:yyyy-MM-dd}";
                }

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var temenosResponse = JsonSerializer.Deserialize<TemenosCustomerResponse>(content);

                return temenosResponse?.Body?.Select(MapToLMSCustomer) ?? Enumerable.Empty<LMSCustomer>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get customers from Temenos");
                throw;
            }
        }

        public async Task<bool> UpdateLoanStatusAsync(string loanId, LoanStatus status)
        {
            try
            {
                await EnsureValidTokenAsync();

                var request = new
                {
                    arrangementId = loanId,
                    status = MapLoanStatusToTemenos(status),
                    effectiveDate = DateTime.UtcNow.ToString("yyyy-MM-dd")
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"/api/v1.0.0/holdings/arrangements/{loanId}/status", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update loan status in Temenos for loan: {LoanId}", loanId);
                return false;
            }
        }

        public async Task<bool> CreateCollectionRecordAsync(LMSCollectionRecord record)
        {
            try
            {
                await EnsureValidTokenAsync();

                var temenosPayment = new
                {
                    header = new
                    {
                        audit = new
                        {
                            requestParse_time = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                        }
                    },
                    body = new
                    {
                        arrangementId = record.LoanId,
                        paymentAmount = record.Amount,
                        paymentMethod = MapPaymentMethodToTemenos(record.Method),
                        valueDate = record.CollectionDate.ToString("yyyy-MM-dd"),
                        transactionReference = record.TransactionId,
                        receiptId = record.ReceiptNumber,
                        narrative = record.Notes,
                        collectorId = record.AgentId
                    }
                };

                var json = JsonSerializer.Serialize(temenosPayment);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/v1.0.0/order/paymentOrders", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create collection record in Temenos");
                return false;
            }
        }

        public async Task<bool> UpdatePaymentStatusAsync(string paymentId, PaymentStatus status)
        {
            try
            {
                await EnsureValidTokenAsync();

                var request = new
                {
                    paymentId = paymentId,
                    status = status.ToString().ToUpper(),
                    statusDate = DateTime.UtcNow.ToString("yyyy-MM-dd")
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"/api/v1.0.0/order/paymentOrders/{paymentId}/status", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update payment status in Temenos");
                return false;
            }
        }

        public async Task<SyncResult> SyncDataAsync(SyncRequest request)
        {
            var result = new SyncResult
            {
                SyncTimestamp = DateTime.UtcNow,
                Success = true,
                Errors = new List<string>()
            };

            try
            {
                // Sync loans
                if (request.EntityTypes.Contains("loans") || !request.EntityTypes.Any())
                {
                    var loans = await GetLoansAsync(request.LastSyncDate);
                    result.RecordsProcessed += loans.Count();
                }

                // Sync customers
                if (request.EntityTypes.Contains("customers") || !request.EntityTypes.Any())
                {
                    var customers = await GetCustomersAsync(request.LastSyncDate);
                    result.RecordsProcessed += customers.Count();
                }

                _logger.LogInformation("Temenos sync completed. Processed {Count} records", result.RecordsProcessed);
            }
            catch (Exception ex)
            {
                result.Success = false;
                ((List<string>)result.Errors).Add(ex.Message);
                _logger.LogError(ex, "Temenos sync failed");
            }

            return result;
        }

        #endregion

        #region ILMSNotificationHandler Implementation

        public async Task StartListeningAsync()
        {
            try
            {
                // Start WebSocket connection for real-time notifications
                _logger.LogInformation("Starting Temenos notification listener");
                
                // Implementation would depend on Temenos notification mechanism
                // This could be WebSocket, Server-Sent Events, or polling
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start Temenos notification listener");
                throw;
            }
        }

        public async Task StopListeningAsync()
        {
            try
            {
                _logger.LogInformation("Stopping Temenos notification listener");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to stop Temenos notification listener");
            }
        }

        public async Task<bool> SubscribeToEventsAsync(IEnumerable<LMSEventType> eventTypes)
        {
            try
            {
                await EnsureValidTokenAsync();

                var subscription = new
                {
                    eventTypes = eventTypes.Select(e => e.ToString()),
                    callbackUrl = _configuration.GetValueOrDefault("CallbackUrl", ""),
                    active = true
                };

                var json = JsonSerializer.Serialize(subscription);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/v1.0.0/system/eventSubscriptions", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to subscribe to Temenos events");
                return false;
            }
        }

        #endregion

        #region ILMSDocumentManager Implementation

        public async Task<string> UploadDocumentAsync(DocumentUploadRequest request)
        {
            try
            {
                await EnsureValidTokenAsync();

                using var formData = new MultipartFormDataContent();
                formData.Add(new StreamContent(request.FileStream), "file", request.FileName);
                formData.Add(new StringContent(request.DocumentType.ToString()), "documentType");
                formData.Add(new StringContent(request.EntityId), "entityId");

                var response = await _httpClient.PostAsync("/api/v1.0.0/holdings/documents", formData);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<TemenosDocumentResponse>(content);

                return result?.Body?.DocumentId ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload document to Temenos");
                throw;
            }
        }

        public async Task<Stream> DownloadDocumentAsync(string documentId)
        {
            try
            {
                await EnsureValidTokenAsync();

                var response = await _httpClient.GetAsync($"/api/v1.0.0/holdings/documents/{documentId}/content");
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStreamAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download document from Temenos");
                throw;
            }
        }

        public async Task<bool> DeleteDocumentAsync(string documentId)
        {
            try
            {
                await EnsureValidTokenAsync();

                var response = await _httpClient.DeleteAsync($"/api/v1.0.0/holdings/documents/{documentId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete document from Temenos");
                return false;
            }
        }

        public async Task<IEnumerable<LMSDocument>> GetDocumentsAsync(string entityId, DocumentType type)
        {
            try
            {
                await EnsureValidTokenAsync();

                var response = await _httpClient.GetAsync($"/api/v1.0.0/holdings/documents?entityId={entityId}&type={type}");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<TemenosDocumentListResponse>(content);

                return result?.Body?.Select(d => new LMSDocument
                {
                    DocumentId = d.DocumentId,
                    FileName = d.FileName,
                    DocumentType = Enum.Parse<DocumentType>(d.DocumentType),
                    EntityId = d.EntityId,
                    UploadDate = d.UploadDate,
                    FileSize = d.FileSize
                }) ?? Enumerable.Empty<LMSDocument>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get documents from Temenos");
                throw;
            }
        }

        public async Task<bool> LinkDocumentToLoanAsync(string documentId, string loanId)
        {
            try
            {
                await EnsureValidTokenAsync();

                var request = new
                {
                    documentId = documentId,
                    arrangementId = loanId,
                    linkType = "LOAN_DOCUMENT"
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/v1.0.0/holdings/documentLinks", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to link document to loan in Temenos");
                return false;
            }
        }

        #endregion

        #region Helper Methods

        private async Task RefreshAccessTokenAsync()
        {
            try
            {
                var tokenRequest = new
                {
                    grant_type = "client_credentials",
                    client_id = _clientId,
                    client_secret = _clientSecret,
                    scope = "accounts payments holdings"
                };

                var json = JsonSerializer.Serialize(tokenRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/oauth/token", content);
                response.EnsureSuccessStatusCode();

                var tokenResponse = await response.Content.ReadAsStringAsync();
                var token = JsonSerializer.Deserialize<TemenosTokenResponse>(tokenResponse);

                _accessToken = token?.AccessToken ?? string.Empty;
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                _logger.LogInformation("Temenos access token refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh Temenos access token");
                throw;
            }
        }

        private async Task EnsureValidTokenAsync()
        {
            if (string.IsNullOrEmpty(_accessToken))
            {
                await RefreshAccessTokenAsync();
            }
        }

        private LMSLoan MapToLMSLoan(TemenosLoan temenosLoan)
        {
            return new LMSLoan
            {
                LoanId = temenosLoan.ArrangementId,
                AccountNumber = temenosLoan.ArrangementId,
                CustomerId = temenosLoan.CustomerId,
                PrincipalAmount = temenosLoan.CommitmentAmount,
                OutstandingAmount = temenosLoan.OutstandingAmount,
                OverdueAmount = temenosLoan.OverdueAmount,
                NextDueDate = temenosLoan.NextPaymentDate,
                OverdueDays = temenosLoan.DaysOverdue,
                InterestRate = temenosLoan.InterestRate,
                EMIAmount = temenosLoan.PaymentAmount,
                Status = MapTemenosStatusToLoanStatus(temenosLoan.Status),
                CreatedDate = temenosLoan.StartDate,
                LastUpdated = temenosLoan.LastModified
            };
        }

        private LMSCustomer MapToLMSCustomer(TemenosCustomer temenosCustomer)
        {
            return new LMSCustomer
            {
                CustomerId = temenosCustomer.CustomerId,
                Name = temenosCustomer.CustomerName,
                Phone = temenosCustomer.PhoneNumber,
                Email = temenosCustomer.EmailAddress,
                Address = temenosCustomer.Address,
                City = temenosCustomer.TownCountry,
                State = temenosCustomer.CountryCode,
                Pincode = temenosCustomer.PostCode,
                CreatedDate = temenosCustomer.DateOfEntry,
                LastUpdated = temenosCustomer.LastModified
            };
        }

        private string MapLoanStatusToTemenos(LoanStatus status)
        {
            return status switch
            {
                LoanStatus.Active => "LIVE",
                LoanStatus.Closed => "CLOSE",
                LoanStatus.Defaulted => "DEFAULT",
                _ => "LIVE"
            };
        }

        private LoanStatus MapTemenosStatusToLoanStatus(string status)
        {
            return status switch
            {
                "LIVE" => LoanStatus.Active,
                "CLOSE" => LoanStatus.Closed,
                "DEFAULT" => LoanStatus.Defaulted,
                _ => LoanStatus.Active
            };
        }

        private string MapPaymentMethodToTemenos(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Cash => "CASH",
                PaymentMethod.UPI => "UPI",
                PaymentMethod.Card => "CARD",
                PaymentMethod.BankTransfer => "AC",
                PaymentMethod.Cheque => "CHQ",
                _ => "CASH"
            };
        }

        #endregion
    }

    #region Temenos Data Models

    public class TemenosTokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string TokenType { get; set; } = string.Empty;
        public int ExpiresIn { get; set; }
    }

    public class TemenosLoanResponse
    {
        public List<TemenosLoan> Body { get; set; } = new();
    }

    public class TemenosLoan
    {
        public string ArrangementId { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal CommitmentAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextPaymentDate { get; set; }
        public int DaysOverdue { get; set; }
        public decimal InterestRate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class TemenosCustomerResponse
    {
        public List<TemenosCustomer> Body { get; set; } = new();
    }

    public class TemenosCustomer
    {
        public string CustomerId { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string TownCountry { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string PostCode { get; set; } = string.Empty;
        public DateTime DateOfEntry { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class TemenosDocumentResponse
    {
        public TemenosDocumentBody Body { get; set; } = new();
    }

    public class TemenosDocumentBody
    {
        public string DocumentId { get; set; } = string.Empty;
    }

    public class TemenosDocumentListResponse
    {
        public List<TemenosDocument> Body { get; set; } = new();
    }

    public class TemenosDocument
    {
        public string DocumentId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public DateTime UploadDate { get; set; }
        public long FileSize { get; set; }
    }

    #endregion
}
