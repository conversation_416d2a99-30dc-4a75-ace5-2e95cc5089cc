using Microsoft.Extensions.Logging;
using FieldEZ.Core.Interfaces;
using FieldEZ.Core.Models;
using System.Text.Json;
using System.Net.Http;
using System.Text;

namespace FieldEZ.Infrastructure.LMS.Plugins
{
    [AutoLoadPlugin(false)]
    public class GenericRestApiPlugin : ILMSDataSync, ILMSWebhookHandler
    {
        private readonly ILogger<GenericRestApiPlugin> _logger;
        private readonly HttpClient _httpClient;
        private Dictionary<string, string> _configuration = new();
        private Dictionary<string, string> _endpoints = new();
        private Dictionary<string, string> _headers = new();

        public string Name => "Generic REST API Plugin";
        public string Version => "1.0.0";
        public string Description => "Generic plugin for REST API-based LMS systems";
        public LMSType Type => LMSType.Custom;
        public bool IsEnabled { get; set; } = true;

        public GenericRestApiPlugin(ILogger<GenericRestApiPlugin> logger, HttpClient httpClient)
        {
            _logger = logger;
            _httpClient = httpClient;
        }

        public async Task InitializeAsync(Dictionary<string, string> configuration)
        {
            _configuration = configuration;

            // Configure base URL
            var baseUrl = configuration.GetValueOrDefault("BaseUrl", "");
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }

            // Configure endpoints
            _endpoints = configuration
                .Where(kvp => kvp.Key.StartsWith("Endpoint."))
                .ToDictionary(
                    kvp => kvp.Key.Substring("Endpoint.".Length),
                    kvp => kvp.Value
                );

            // Configure headers
            _headers = configuration
                .Where(kvp => kvp.Key.StartsWith("Header."))
                .ToDictionary(
                    kvp => kvp.Key.Substring("Header.".Length),
                    kvp => kvp.Value
                );

            // Set headers
            foreach (var header in _headers)
            {
                _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            // Configure authentication
            var authType = configuration.GetValueOrDefault("AuthType", "");
            switch (authType.ToLower())
            {
                case "bearer":
                    var token = configuration.GetValueOrDefault("BearerToken", "");
                    if (!string.IsNullOrEmpty(token))
                    {
                        _httpClient.DefaultRequestHeaders.Authorization = 
                            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                    }
                    break;
                case "basic":
                    var username = configuration.GetValueOrDefault("Username", "");
                    var password = configuration.GetValueOrDefault("Password", "");
                    if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                    {
                        var authToken = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
                        _httpClient.DefaultRequestHeaders.Authorization = 
                            new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authToken);
                    }
                    break;
                case "apikey":
                    var apiKey = configuration.GetValueOrDefault("ApiKey", "");
                    var apiKeyHeader = configuration.GetValueOrDefault("ApiKeyHeader", "X-API-Key");
                    if (!string.IsNullOrEmpty(apiKey))
                    {
                        _httpClient.DefaultRequestHeaders.Add(apiKeyHeader, apiKey);
                    }
                    break;
            }

            _logger.LogInformation("Generic REST API plugin initialized with base URL: {BaseUrl}", baseUrl);
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var healthEndpoint = _endpoints.GetValueOrDefault("Health", "/health");
                var response = await _httpClient.GetAsync(healthEndpoint);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic REST API connection test failed");
                return false;
            }
        }

        public async Task<LMSHealthStatus> GetHealthStatusAsync()
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var healthEndpoint = _endpoints.GetValueOrDefault("Health", "/health");
                var response = await _httpClient.GetAsync(healthEndpoint);
                var responseTime = DateTime.UtcNow - startTime;

                return new LMSHealthStatus
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    Status = response.IsSuccessStatusCode ? "Healthy" : $"Error: {response.StatusCode}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = responseTime,
                    Details = new Dictionary<string, object>
                    {
                        ["StatusCode"] = (int)response.StatusCode,
                        ["Endpoint"] = healthEndpoint
                    }
                };
            }
            catch (Exception ex)
            {
                return new LMSHealthStatus
                {
                    IsHealthy = false,
                    Status = $"Error: {ex.Message}",
                    LastChecked = DateTime.UtcNow,
                    ResponseTime = DateTime.UtcNow - startTime
                };
            }
        }

        #region ILMSDataSync Implementation

        public async Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("GetLoans", "/api/loans");
                if (lastSyncDate.HasValue)
                {
                    var dateParam = _configuration.GetValueOrDefault("DateParameter", "lastModified");
                    var dateFormat = _configuration.GetValueOrDefault("DateFormat", "yyyy-MM-ddTHH:mm:ssZ");
                    endpoint += $"?{dateParam}={lastSyncDate.Value.ToString(dateFormat)}";
                }

                var response = await _httpClient.GetAsync(endpoint);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                
                // Try to deserialize based on configuration
                var dataPath = _configuration.GetValueOrDefault("LoansDataPath", "");
                var loans = ExtractDataFromResponse<GenericLoan>(content, dataPath);

                return loans.Select(MapToLMSLoan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get loans from Generic REST API");
                throw;
            }
        }

        public async Task<IEnumerable<LMSCustomer>> GetCustomersAsync(DateTime? lastSyncDate = null)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("GetCustomers", "/api/customers");
                if (lastSyncDate.HasValue)
                {
                    var dateParam = _configuration.GetValueOrDefault("DateParameter", "lastModified");
                    var dateFormat = _configuration.GetValueOrDefault("DateFormat", "yyyy-MM-ddTHH:mm:ssZ");
                    endpoint += $"?{dateParam}={lastSyncDate.Value.ToString(dateFormat)}";
                }

                var response = await _httpClient.GetAsync(endpoint);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                
                var dataPath = _configuration.GetValueOrDefault("CustomersDataPath", "");
                var customers = ExtractDataFromResponse<GenericCustomer>(content, dataPath);

                return customers.Select(MapToLMSCustomer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get customers from Generic REST API");
                throw;
            }
        }

        public async Task<bool> UpdateLoanStatusAsync(string loanId, LoanStatus status)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("UpdateLoanStatus", $"/api/loans/{loanId}/status");
                endpoint = endpoint.Replace("{loanId}", loanId);

                var request = new Dictionary<string, object>
                {
                    ["status"] = status.ToString(),
                    ["updatedDate"] = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync(endpoint, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update loan status via Generic REST API");
                return false;
            }
        }

        public async Task<bool> CreateCollectionRecordAsync(LMSCollectionRecord record)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("CreateCollection", "/api/collections");

                var request = new Dictionary<string, object>
                {
                    ["loanId"] = record.LoanId,
                    ["agentId"] = record.AgentId,
                    ["amount"] = record.Amount,
                    ["method"] = record.Method.ToString(),
                    ["collectionDate"] = record.CollectionDate,
                    ["transactionId"] = record.TransactionId,
                    ["receiptNumber"] = record.ReceiptNumber,
                    ["notes"] = record.Notes
                };

                // Add custom fields
                foreach (var field in record.CustomFields)
                {
                    request[field.Key] = field.Value;
                }

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create collection record via Generic REST API");
                return false;
            }
        }

        public async Task<bool> UpdatePaymentStatusAsync(string paymentId, PaymentStatus status)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("UpdatePaymentStatus", $"/api/payments/{paymentId}/status");
                endpoint = endpoint.Replace("{paymentId}", paymentId);

                var request = new Dictionary<string, object>
                {
                    ["status"] = status.ToString(),
                    ["updatedDate"] = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync(endpoint, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update payment status via Generic REST API");
                return false;
            }
        }

        public async Task<SyncResult> SyncDataAsync(SyncRequest request)
        {
            var result = new SyncResult
            {
                SyncTimestamp = DateTime.UtcNow,
                Success = true,
                Errors = new List<string>()
            };

            try
            {
                // Sync loans
                if (request.EntityTypes.Contains("loans") || !request.EntityTypes.Any())
                {
                    var loans = await GetLoansAsync(request.LastSyncDate);
                    result.RecordsProcessed += loans.Count();
                }

                // Sync customers
                if (request.EntityTypes.Contains("customers") || !request.EntityTypes.Any())
                {
                    var customers = await GetCustomersAsync(request.LastSyncDate);
                    result.RecordsProcessed += customers.Count();
                }

                _logger.LogInformation("Generic REST API sync completed. Processed {Count} records", result.RecordsProcessed);
            }
            catch (Exception ex)
            {
                result.Success = false;
                ((List<string>)result.Errors).Add(ex.Message);
                _logger.LogError(ex, "Generic REST API sync failed");
            }

            return result;
        }

        #endregion

        #region ILMSWebhookHandler Implementation

        public async Task<WebhookResponse> HandleWebhookAsync(WebhookRequest request)
        {
            try
            {
                _logger.LogInformation("Processing webhook: {EventType}", request.EventType);

                // Parse webhook payload based on configuration
                var payload = JsonSerializer.Deserialize<Dictionary<string, object>>(request.Payload);

                // Process webhook based on event type
                switch (request.EventType)
                {
                    case LMSEventType.LoanCreated:
                    case LMSEventType.LoanUpdated:
                        await ProcessLoanWebhook(payload);
                        break;
                    case LMSEventType.PaymentReceived:
                        await ProcessPaymentWebhook(payload);
                        break;
                    default:
                        _logger.LogWarning("Unhandled webhook event type: {EventType}", request.EventType);
                        break;
                }

                return new WebhookResponse
                {
                    Success = true,
                    Message = "Webhook processed successfully",
                    StatusCode = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process webhook");
                return new WebhookResponse
                {
                    Success = false,
                    Message = ex.Message,
                    StatusCode = 500
                };
            }
        }

        public async Task<bool> RegisterWebhookAsync(string callbackUrl, IEnumerable<LMSEventType> eventTypes)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("RegisterWebhook", "/api/webhooks");

                var request = new Dictionary<string, object>
                {
                    ["callbackUrl"] = callbackUrl,
                    ["eventTypes"] = eventTypes.Select(e => e.ToString()),
                    ["active"] = true
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register webhook");
                return false;
            }
        }

        public async Task<bool> UnregisterWebhookAsync(string webhookId)
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("UnregisterWebhook", $"/api/webhooks/{webhookId}");
                endpoint = endpoint.Replace("{webhookId}", webhookId);

                var response = await _httpClient.DeleteAsync(endpoint);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unregister webhook");
                return false;
            }
        }

        public async Task<IEnumerable<WebhookRegistration>> GetActiveWebhooksAsync()
        {
            try
            {
                var endpoint = _endpoints.GetValueOrDefault("GetWebhooks", "/api/webhooks");
                var response = await _httpClient.GetAsync(endpoint);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var webhooks = ExtractDataFromResponse<GenericWebhook>(content, "");

                return webhooks.Select(w => new WebhookRegistration
                {
                    WebhookId = w.Id,
                    CallbackUrl = w.CallbackUrl,
                    EventTypes = w.EventTypes.Select(e => Enum.Parse<LMSEventType>(e)),
                    IsActive = w.Active,
                    CreatedDate = w.CreatedDate
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get active webhooks");
                return Enumerable.Empty<WebhookRegistration>();
            }
        }

        #endregion

        #region Helper Methods

        private IEnumerable<T> ExtractDataFromResponse<T>(string jsonContent, string dataPath)
        {
            try
            {
                using var document = JsonDocument.Parse(jsonContent);
                var root = document.RootElement;

                // Navigate to data path if specified
                if (!string.IsNullOrEmpty(dataPath))
                {
                    var pathParts = dataPath.Split('.');
                    var current = root;
                    
                    foreach (var part in pathParts)
                    {
                        if (current.TryGetProperty(part, out var property))
                        {
                            current = property;
                        }
                        else
                        {
                            return Enumerable.Empty<T>();
                        }
                    }
                    
                    return JsonSerializer.Deserialize<IEnumerable<T>>(current.GetRawText()) ?? Enumerable.Empty<T>();
                }

                // Try to deserialize directly
                if (root.ValueKind == JsonValueKind.Array)
                {
                    return JsonSerializer.Deserialize<IEnumerable<T>>(jsonContent) ?? Enumerable.Empty<T>();
                }
                else
                {
                    var item = JsonSerializer.Deserialize<T>(jsonContent);
                    return item != null ? new[] { item } : Enumerable.Empty<T>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to extract data from response");
                return Enumerable.Empty<T>();
            }
        }

        private LMSLoan MapToLMSLoan(GenericLoan genericLoan)
        {
            return new LMSLoan
            {
                LoanId = genericLoan.Id,
                AccountNumber = genericLoan.AccountNumber,
                CustomerId = genericLoan.CustomerId,
                PrincipalAmount = genericLoan.PrincipalAmount,
                OutstandingAmount = genericLoan.OutstandingAmount,
                OverdueAmount = genericLoan.OverdueAmount,
                NextDueDate = genericLoan.NextDueDate,
                OverdueDays = genericLoan.OverdueDays,
                InterestRate = genericLoan.InterestRate,
                EMIAmount = genericLoan.EMIAmount,
                Status = Enum.TryParse<LoanStatus>(genericLoan.Status, out var status) ? status : LoanStatus.Active,
                CreatedDate = genericLoan.CreatedDate,
                LastUpdated = genericLoan.LastUpdated,
                CustomFields = genericLoan.CustomFields
            };
        }

        private LMSCustomer MapToLMSCustomer(GenericCustomer genericCustomer)
        {
            return new LMSCustomer
            {
                CustomerId = genericCustomer.Id,
                Name = genericCustomer.Name,
                Phone = genericCustomer.Phone,
                Email = genericCustomer.Email,
                Address = genericCustomer.Address,
                City = genericCustomer.City,
                State = genericCustomer.State,
                Pincode = genericCustomer.Pincode,
                CreatedDate = genericCustomer.CreatedDate,
                LastUpdated = genericCustomer.LastUpdated,
                CustomFields = genericCustomer.CustomFields
            };
        }

        private async Task ProcessLoanWebhook(Dictionary<string, object>? payload)
        {
            if (payload != null)
            {
                _logger.LogInformation("Processing loan webhook with {Count} fields", payload.Count);
                // Process loan webhook data
            }
        }

        private async Task ProcessPaymentWebhook(Dictionary<string, object>? payload)
        {
            if (payload != null)
            {
                _logger.LogInformation("Processing payment webhook with {Count} fields", payload.Count);
                // Process payment webhook data
            }
        }

        #endregion
    }

    #region Generic Data Models

    public class GenericLoan
    {
        public string Id { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal PrincipalAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int OverdueDays { get; set; }
        public decimal InterestRate { get; set; }
        public decimal EMIAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class GenericCustomer
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Pincode { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class GenericWebhook
    {
        public string Id { get; set; } = string.Empty;
        public string CallbackUrl { get; set; } = string.Empty;
        public List<string> EventTypes { get; set; } = new();
        public bool Active { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    #endregion
}
