using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using FieldEZ.Core.Interfaces;
using FieldEZ.Core.Models;
using System.Reflection;

namespace FieldEZ.Infrastructure.LMS
{
    public class LMSPluginManager : ILMSPluginManager
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<LMSPluginManager> _logger;
        private readonly IConfiguration _configuration;
        private readonly Dictionary<string, ILMSPlugin> _plugins;
        private readonly Dictionary<Type, List<ILMSPlugin>> _pluginsByInterface;

        public LMSPluginManager(
            IServiceProvider serviceProvider,
            ILogger<LMSPluginManager> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
            _plugins = new Dictionary<string, ILMSPlugin>();
            _pluginsByInterface = new Dictionary<Type, List<ILMSPlugin>>();
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing LMS Plugin Manager");

            try
            {
                // Load plugins from configuration
                var pluginConfigs = _configuration.GetSection("LMSPlugins").Get<List<PluginConfiguration>>() ?? new List<PluginConfiguration>();

                foreach (var config in pluginConfigs.Where(c => c.Enabled))
                {
                    await LoadPluginAsync(config);
                }

                // Auto-discover plugins in assemblies
                await DiscoverPluginsAsync();

                _logger.LogInformation("LMS Plugin Manager initialized with {PluginCount} plugins", _plugins.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize LMS Plugin Manager");
                throw;
            }
        }

        public async Task<bool> LoadPluginAsync(PluginConfiguration config)
        {
            try
            {
                _logger.LogInformation("Loading plugin: {PluginName}", config.Name);

                // Create plugin instance
                var pluginType = Type.GetType(config.TypeName);
                if (pluginType == null)
                {
                    _logger.LogError("Plugin type not found: {TypeName}", config.TypeName);
                    return false;
                }

                var plugin = (ILMSPlugin)ActivatorUtilities.CreateInstance(_serviceProvider, pluginType);
                
                // Initialize plugin
                await plugin.InitializeAsync(config.Configuration);
                plugin.IsEnabled = config.Enabled;

                // Test connection
                var connectionTest = await plugin.TestConnectionAsync();
                if (!connectionTest)
                {
                    _logger.LogWarning("Plugin {PluginName} failed connection test", config.Name);
                    if (config.RequireConnection)
                    {
                        return false;
                    }
                }

                // Register plugin
                _plugins[plugin.Name] = plugin;
                RegisterPluginByInterfaces(plugin);

                _logger.LogInformation("Plugin {PluginName} loaded successfully", plugin.Name);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load plugin: {PluginName}", config.Name);
                return false;
            }
        }

        public async Task<bool> UnloadPluginAsync(string pluginName)
        {
            try
            {
                if (!_plugins.TryGetValue(pluginName, out var plugin))
                {
                    return false;
                }

                // Stop notification handlers
                if (plugin is ILMSNotificationHandler notificationHandler)
                {
                    await notificationHandler.StopListeningAsync();
                }

                // Remove from registrations
                _plugins.Remove(pluginName);
                UnregisterPluginFromInterfaces(plugin);

                _logger.LogInformation("Plugin {PluginName} unloaded successfully", pluginName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unload plugin: {PluginName}", pluginName);
                return false;
            }
        }

        public T? GetPlugin<T>(string? pluginName = null) where T : class, ILMSPlugin
        {
            if (!string.IsNullOrEmpty(pluginName))
            {
                return _plugins.TryGetValue(pluginName, out var plugin) ? plugin as T : null;
            }

            // Return first plugin of type T
            return _pluginsByInterface.TryGetValue(typeof(T), out var plugins) 
                ? plugins.FirstOrDefault() as T 
                : null;
        }

        public IEnumerable<T> GetPlugins<T>() where T : class, ILMSPlugin
        {
            return _pluginsByInterface.TryGetValue(typeof(T), out var plugins)
                ? plugins.OfType<T>()
                : Enumerable.Empty<T>();
        }

        public IEnumerable<ILMSPlugin> GetAllPlugins()
        {
            return _plugins.Values;
        }

        public async Task<IEnumerable<LMSHealthStatus>> GetHealthStatusAsync()
        {
            var healthStatuses = new List<LMSHealthStatus>();

            foreach (var plugin in _plugins.Values)
            {
                try
                {
                    var status = await plugin.GetHealthStatusAsync();
                    healthStatuses.Add(status);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get health status for plugin: {PluginName}", plugin.Name);
                    healthStatuses.Add(new LMSHealthStatus
                    {
                        IsHealthy = false,
                        Status = $"Error: {ex.Message}",
                        LastChecked = DateTime.UtcNow
                    });
                }
            }

            return healthStatuses;
        }

        public async Task<SyncResult> SyncAllDataAsync(SyncRequest request)
        {
            var overallResult = new SyncResult
            {
                Success = true,
                SyncTimestamp = DateTime.UtcNow,
                Errors = new List<string>()
            };

            var dataSyncPlugins = GetPlugins<ILMSDataSync>();

            foreach (var plugin in dataSyncPlugins)
            {
                try
                {
                    _logger.LogInformation("Starting data sync for plugin: {PluginName}", plugin.Name);
                    
                    var result = await plugin.SyncDataAsync(request);
                    
                    overallResult.RecordsProcessed += result.RecordsProcessed;
                    overallResult.RecordsUpdated += result.RecordsUpdated;
                    overallResult.RecordsCreated += result.RecordsCreated;
                    overallResult.RecordsSkipped += result.RecordsSkipped;

                    if (!result.Success)
                    {
                        overallResult.Success = false;
                        ((List<string>)overallResult.Errors).AddRange(result.Errors);
                    }

                    _logger.LogInformation("Data sync completed for plugin: {PluginName}. Processed: {Processed}, Updated: {Updated}, Created: {Created}",
                        plugin.Name, result.RecordsProcessed, result.RecordsUpdated, result.RecordsCreated);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Data sync failed for plugin: {PluginName}", plugin.Name);
                    overallResult.Success = false;
                    ((List<string>)overallResult.Errors).Add($"Plugin {plugin.Name}: {ex.Message}");
                }
            }

            return overallResult;
        }

        private async Task DiscoverPluginsAsync()
        {
            try
            {
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                
                foreach (var assembly in assemblies)
                {
                    var pluginTypes = assembly.GetTypes()
                        .Where(t => t.IsClass && !t.IsAbstract && typeof(ILMSPlugin).IsAssignableFrom(t))
                        .ToList();

                    foreach (var pluginType in pluginTypes)
                    {
                        // Skip if already loaded
                        if (_plugins.Values.Any(p => p.GetType() == pluginType))
                            continue;

                        try
                        {
                            var plugin = (ILMSPlugin)ActivatorUtilities.CreateInstance(_serviceProvider, pluginType);
                            
                            // Check if plugin should be auto-loaded
                            var autoLoadAttribute = pluginType.GetCustomAttribute<AutoLoadPluginAttribute>();
                            if (autoLoadAttribute != null)
                            {
                                await plugin.InitializeAsync(new Dictionary<string, string>());
                                _plugins[plugin.Name] = plugin;
                                RegisterPluginByInterfaces(plugin);
                                
                                _logger.LogInformation("Auto-loaded plugin: {PluginName}", plugin.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to auto-load plugin: {PluginType}", pluginType.Name);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to discover plugins");
            }
        }

        private void RegisterPluginByInterfaces(ILMSPlugin plugin)
        {
            var interfaces = plugin.GetType().GetInterfaces()
                .Where(i => i != typeof(ILMSPlugin) && typeof(ILMSPlugin).IsAssignableFrom(i));

            foreach (var interfaceType in interfaces)
            {
                if (!_pluginsByInterface.ContainsKey(interfaceType))
                {
                    _pluginsByInterface[interfaceType] = new List<ILMSPlugin>();
                }
                _pluginsByInterface[interfaceType].Add(plugin);
            }
        }

        private void UnregisterPluginFromInterfaces(ILMSPlugin plugin)
        {
            foreach (var kvp in _pluginsByInterface.ToList())
            {
                kvp.Value.Remove(plugin);
                if (!kvp.Value.Any())
                {
                    _pluginsByInterface.Remove(kvp.Key);
                }
            }
        }
    }

    public interface ILMSPluginManager
    {
        Task InitializeAsync();
        Task<bool> LoadPluginAsync(PluginConfiguration config);
        Task<bool> UnloadPluginAsync(string pluginName);
        T? GetPlugin<T>(string? pluginName = null) where T : class, ILMSPlugin;
        IEnumerable<T> GetPlugins<T>() where T : class, ILMSPlugin;
        IEnumerable<ILMSPlugin> GetAllPlugins();
        Task<IEnumerable<LMSHealthStatus>> GetHealthStatusAsync();
        Task<SyncResult> SyncAllDataAsync(SyncRequest request);
    }

    public class PluginConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public string TypeName { get; set; } = string.Empty;
        public bool Enabled { get; set; } = true;
        public bool RequireConnection { get; set; } = true;
        public Dictionary<string, string> Configuration { get; set; } = new();
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class AutoLoadPluginAttribute : Attribute
    {
        public bool AutoLoad { get; }

        public AutoLoadPluginAttribute(bool autoLoad = true)
        {
            AutoLoad = autoLoad;
        }
    }
}
