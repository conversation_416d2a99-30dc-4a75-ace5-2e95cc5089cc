using Microsoft.EntityFrameworkCore;
using FieldEZ.Core.Models;
using NetTopologySuite.Geometries;

namespace FieldEZ.Infrastructure.Data
{
    public class FieldEZDbContext : DbContext
    {
        public FieldEZDbContext(DbContextOptions<FieldEZDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Agent> Agents { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Loan> Loans { get; set; }
        public DbSet<Lead> Leads { get; set; }
        public DbSet<Visit> Visits { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<AgentLocation> AgentLocations { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<SyncLog> SyncLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Agent entity
            modelBuilder.Entity<Agent>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.EmployeeId).IsUnique();
                entity.HasIndex(e => e.Phone).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.LastActiveAt);

                entity.Property(e => e.Status)
                    .HasConversion<string>();

                entity.Property(e => e.TotalAmountCollected)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.SuccessRate)
                    .HasColumnType("decimal(5,2)");

                // Configure spatial properties
                entity.Property(e => e.CurrentLocation)
                    .HasColumnType("geography");

                entity.Property(e => e.TerritoryBounds)
                    .HasColumnType("geography");

                entity.Property(e => e.BaseLocation)
                    .HasColumnType("geography");
            });

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Phone);
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.City);

                entity.Property(e => e.AnnualIncome)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.Coordinates)
                    .HasColumnType("geography");
            });

            // Configure Loan entity
            modelBuilder.Entity<Loan>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.AccountNumber).IsUnique();
                entity.HasIndex(e => e.CustomerId);
                entity.HasIndex(e => e.NextDueDate);
                entity.HasIndex(e => e.OverdueDays);

                entity.Property(e => e.PrincipalAmount)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.InterestRate)
                    .HasColumnType("decimal(5,2)");

                entity.Property(e => e.EMIAmount)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.OutstandingAmount)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.OverdueAmount)
                    .HasColumnType("decimal(15,2)");

                // Foreign key relationship
                entity.HasOne(e => e.Customer)
                    .WithMany(c => c.Loans)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Lead entity
            modelBuilder.Entity<Lead>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.AssignedAgentId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.Deadline);
                entity.HasIndex(e => e.CustomerId);
                entity.HasIndex(e => e.LoanId);

                entity.Property(e => e.Status)
                    .HasConversion<string>();

                entity.Property(e => e.Priority)
                    .HasConversion<string>();

                entity.Property(e => e.ExpectedCollectionAmount)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.TotalCollected)
                    .HasColumnType("decimal(15,2)");

                // Foreign key relationships
                entity.HasOne(e => e.Loan)
                    .WithMany(l => l.Leads)
                    .HasForeignKey(e => e.LoanId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Customer)
                    .WithMany(c => c.Leads)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.AssignedAgent)
                    .WithMany(a => a.AssignedLeads)
                    .HasForeignKey(e => e.AssignedAgentId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Visit entity
            modelBuilder.Entity<Visit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.LeadId);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.VisitDate);
                entity.HasIndex(e => e.Result);

                entity.Property(e => e.Result)
                    .HasConversion<string>();

                entity.Property(e => e.Location)
                    .HasColumnType("geography");

                // Foreign key relationships
                entity.HasOne(e => e.Lead)
                    .WithMany(l => l.Visits)
                    .HasForeignKey(e => e.LeadId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.Visits)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Payment entity
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.LeadId);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.PaymentTimestamp);
                entity.HasIndex(e => e.Method);
                entity.HasIndex(e => e.ReceiptNumber).IsUnique();
                entity.HasIndex(e => e.TransactionId);

                entity.Property(e => e.Amount)
                    .HasColumnType("decimal(15,2)");

                entity.Property(e => e.Method)
                    .HasConversion<string>();

                entity.Property(e => e.Status)
                    .HasConversion<string>();

                entity.Property(e => e.PaymentLocation)
                    .HasColumnType("geography");

                // Foreign key relationships
                entity.HasOne(e => e.Lead)
                    .WithMany(l => l.Payments)
                    .HasForeignKey(e => e.LeadId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Visit)
                    .WithMany(v => v.Payments)
                    .HasForeignKey(e => e.VisitId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.Payments)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Document entity
            modelBuilder.Entity<Document>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.LeadId);
                entity.HasIndex(e => e.VisitId);
                entity.HasIndex(e => e.PaymentId);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.DocumentType);

                entity.Property(e => e.DocumentType)
                    .HasConversion<string>();

                // Foreign key relationships
                entity.HasOne(e => e.Lead)
                    .WithMany(l => l.Documents)
                    .HasForeignKey(e => e.LeadId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Visit)
                    .WithMany(v => v.Documents)
                    .HasForeignKey(e => e.VisitId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Payment)
                    .WithMany(p => p.Documents)
                    .HasForeignKey(e => e.PaymentId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.Documents)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure AgentLocation entity
            modelBuilder.Entity<AgentLocation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.Timestamp);

                entity.Property(e => e.Location)
                    .HasColumnType("geography");

                entity.Property(e => e.Accuracy)
                    .HasColumnType("decimal(8,2)");

                // Foreign key relationship
                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.LocationHistory)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Notification entity
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.IsRead);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Priority);

                entity.Property(e => e.Type)
                    .HasConversion<string>();

                entity.Property(e => e.Priority)
                    .HasConversion<string>();

                // Foreign key relationship
                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.Notifications)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure SyncLog entity
            modelBuilder.Entity<SyncLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.AgentId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.StartedAt);

                entity.Property(e => e.SyncType)
                    .HasConversion<string>();

                entity.Property(e => e.Status)
                    .HasConversion<string>();

                // Foreign key relationship
                entity.HasOne(e => e.Agent)
                    .WithMany(a => a.SyncLogs)
                    .HasForeignKey(e => e.AgentId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure check constraints
            modelBuilder.Entity<Agent>()
                .ToTable(t => t.HasCheckConstraint("CK_Agents_SuccessRate", "[SuccessRate] >= 0 AND [SuccessRate] <= 100"));

            modelBuilder.Entity<Loan>()
                .ToTable(t => t.HasCheckConstraint("CK_Loans_Amounts", 
                    "[PrincipalAmount] > 0 AND [OutstandingAmount] >= 0 AND [OverdueAmount] >= 0 AND [EMIAmount] > 0"));

            modelBuilder.Entity<Payment>()
                .ToTable(t => t.HasCheckConstraint("CK_Payments_Amount", "[Amount] > 0"));

            modelBuilder.Entity<Visit>()
                .ToTable(t => t.HasCheckConstraint("CK_Visits_Duration", "[DurationMinutes] >= 0 OR [DurationMinutes] IS NULL"));
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Modified)
                .Where(e => e.Entity.GetType().GetProperty("UpdatedAt") != null);

            foreach (var entry in entries)
            {
                entry.Property("UpdatedAt").CurrentValue = DateTime.UtcNow;
            }
        }
    }
}
