{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.API/FieldEZ.API.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.API/FieldEZ.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.API/FieldEZ.API.csproj", "projectName": "FieldEZ.API", "projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.API/FieldEZ.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj"}, "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj", "projectName": "FieldEZ.Core", "projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "NetTopologySuite": {"target": "Package", "version": "[2.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj", "projectName": "FieldEZ.Infrastructure", "projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/Loan Collections/backend/FieldEZ.Core/FieldEZ.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.0, )"}, "NetTopologySuite.IO.SqlServerBytes": {"target": "Package", "version": "[2.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}