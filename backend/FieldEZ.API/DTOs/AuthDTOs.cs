using System.ComponentModel.DataAnnotations;

namespace FieldEZ.API.DTOs
{
    public class LoginRequestDto
    {
        [Required]
        [Phone]
        public string Phone { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string Password { get; set; } = string.Empty;

        public string? DeviceId { get; set; }
    }

    public class LoginResponseDto
    {
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public int ExpiresIn { get; set; }
        public AgentDto Agent { get; set; } = null!;
    }

    public class RefreshTokenRequestDto
    {
        [Required]
        public string RefreshToken { get; set; } = string.Empty;
    }

    public class RefreshTokenResponseDto
    {
        public string AccessToken { get; set; } = string.Empty;
        public int ExpiresIn { get; set; }
    }

    public class RegisterRequestDto
    {
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Phone]
        public string Phone { get; set; } = string.Empty;

        [EmailAddress]
        public string? Email { get; set; }

        [Required]
        [StringLength(50)]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string Password { get; set; } = string.Empty;

        [StringLength(50)]
        public string? Role { get; set; }
    }

    public class AgentDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string EmployeeId { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public LocationDto? CurrentLocation { get; set; }
        public PerformanceDto? Performance { get; set; }
        public DateTime? LastActiveAt { get; set; }
    }

    public class LocationDto
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? Address { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class PerformanceDto
    {
        public int CollectionsToday { get; set; }
        public decimal AmountCollectedToday { get; set; }
        public decimal SuccessRate { get; set; }
        public int AvgCollectionTime { get; set; }
        public int TotalLeads { get; set; }
        public int CompletedLeads { get; set; }
    }
}

namespace FieldEZ.API.DTOs
{
    public class LeadDto
    {
        public Guid Id { get; set; }
        public CustomerDto Customer { get; set; } = null!;
        public LoanDto Loan { get; set; } = null!;
        public AssignmentDto Assignment { get; set; } = null!;
        public string Status { get; set; } = string.Empty;
        public List<VisitDto> VisitHistory { get; set; } = new();
        public List<PaymentDto> PaymentHistory { get; set; } = new();
    }

    public class CustomerDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string? Email { get; set; }
        public AddressDto Address { get; set; } = null!;
    }

    public class AddressDto
    {
        public string? AddressLine1 { get; set; }
        public string? AddressLine2 { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Pincode { get; set; }
        public string Country { get; set; } = "India";
        public LocationDto? Coordinates { get; set; }
    }

    public class LoanDto
    {
        public Guid Id { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public decimal PrincipalAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int OverdueDays { get; set; }
        public decimal InterestRate { get; set; }
        public decimal EMIAmount { get; set; }
    }

    public class AssignmentDto
    {
        public DateTime? AssignedAt { get; set; }
        public string Priority { get; set; } = string.Empty;
        public decimal? ExpectedCollectionAmount { get; set; }
        public DateTime? Deadline { get; set; }
        public int VisitCount { get; set; }
        public int SuccessfulVisits { get; set; }
        public decimal TotalCollected { get; set; }
    }

    public class VisitDto
    {
        public Guid Id { get; set; }
        public DateTime VisitDate { get; set; }
        public string Result { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public LocationDto? Location { get; set; }
        public int? DurationMinutes { get; set; }
        public DateTime? NextVisitScheduled { get; set; }
        public string? FollowUpNotes { get; set; }
    }

    public class PaymentDto
    {
        public Guid Id { get; set; }
        public decimal Amount { get; set; }
        public string Method { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime PaymentTimestamp { get; set; }
        public string? TransactionId { get; set; }
        public string? ReceiptNumber { get; set; }
        public string? ReceiptUrl { get; set; }
        public LocationDto? PaymentLocation { get; set; }
    }

    public class CreateVisitRequestDto
    {
        [Required]
        public DateTime Timestamp { get; set; }

        public LocationDto? Location { get; set; }

        [Required]
        public string Result { get; set; } = string.Empty;

        public string? Notes { get; set; }

        public DateTime? NextVisitScheduled { get; set; }

        public string? FollowUpNotes { get; set; }

        public List<string>? Photos { get; set; } // Base64 encoded images
    }

    public class CollectPaymentRequestDto
    {
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }

        [Required]
        public string Method { get; set; } = string.Empty;

        public PaymentDetailsDto? TransactionDetails { get; set; }

        public LocationDto? Location { get; set; }

        [Required]
        public DateTime Timestamp { get; set; }

        public ReceiptDataDto? ReceiptData { get; set; }
    }

    public class PaymentDetailsDto
    {
        public string? UPIId { get; set; }
        public string? TransactionId { get; set; }
        public string? Gateway { get; set; }
        public string? CardLastFour { get; set; }
        public string? CardType { get; set; }
    }

    public class ReceiptDataDto
    {
        public string? CustomerSignature { get; set; } // Base64 encoded
        public List<string>? Photos { get; set; } // Base64 encoded images
    }

    public class UpdateLocationRequestDto
    {
        [Required]
        public double Latitude { get; set; }

        [Required]
        public double Longitude { get; set; }

        public decimal? Accuracy { get; set; }

        [Required]
        public DateTime Timestamp { get; set; }
    }

    public class DashboardDataDto
    {
        public DashboardStatsDto Stats { get; set; } = null!;
        public List<LeadDto> TodaysLeads { get; set; } = new();
        public List<PaymentDto> RecentPayments { get; set; } = new();
        public List<NotificationDto> Notifications { get; set; } = new();
    }

    public class DashboardStatsDto
    {
        public int PendingLeads { get; set; }
        public int VisitedLeads { get; set; }
        public int CollectedLeads { get; set; }
        public int TodaysVisits { get; set; }
        public int TodaysPayments { get; set; }
        public decimal TodaysCollection { get; set; }
        public decimal TargetAmount { get; set; }
        public decimal AchievementRate { get; set; }
    }

    public class NotificationDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public DateTime SentAt { get; set; }
        public bool IsRead { get; set; }
        public string? ActionUrl { get; set; }
    }

    // LMS Integration DTOs
    public class LMSPluginDto
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
    }

    public class LMSHealthStatusDto
    {
        public string PluginName { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
        public int ResponseTimeMs { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
    }

    public class SyncRequestDto
    {
        public DateTime? LastSyncDate { get; set; }
        public IEnumerable<string>? EntityTypes { get; set; }
        public Dictionary<string, object>? Filters { get; set; }
        public int BatchSize { get; set; } = 100;
        public bool FullSync { get; set; } = false;
    }

    public class SyncResultDto
    {
        public bool Success { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsCreated { get; set; }
        public int RecordsSkipped { get; set; }
        public DateTime SyncTimestamp { get; set; }
        public List<string> Errors { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class WebhookRequestDto
    {
        public string WebhookId { get; set; } = string.Empty;
        public string EventType { get; set; } = string.Empty;
        public string Payload { get; set; } = string.Empty;
        public Dictionary<string, string>? Headers { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class WebhookResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int StatusCode { get; set; }
    }

    public class LMSLoanDto
    {
        public string LoanId { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal PrincipalAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int OverdueDays { get; set; }
        public decimal InterestRate { get; set; }
        public decimal EMIAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class LMSCustomerDto
    {
        public string CustomerId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Pincode { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class LMSCollectionRecordDto
    {
        public string LoanId { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Method { get; set; } = string.Empty;
        public DateTime CollectionDate { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
    }

    public class UpdateLoanStatusRequestDto
    {
        public string Status { get; set; } = string.Empty;
    }
}
