{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=FieldEZ;Trusted_Connection=true;TrustServerCertificate=true;"}, "Jwt": {"Secret": "your-super-secret-jwt-key-here-make-it-long-and-secure", "Issuer": "FieldEZ.API", "Audience": "FieldEZ.Client", "ExpirationMinutes": 60}, "LMSPlugins": [{"Name": "Finacle LMS Plugin", "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.FinaclePlugin, FieldEZ.Infrastructure", "Enabled": false, "RequireConnection": true, "Configuration": {"BaseUrl": "https://finacle-api.example.com", "ApiKey": "your-finacle-api-key", "Username": "your-finacle-username", "Password": "your-finacle-password", "Timeout": "30000"}}, {"Name": "Temenos T24 Plugin", "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.TemenosPlugin, FieldEZ.Infrastructure", "Enabled": false, "RequireConnection": true, "Configuration": {"BaseUrl": "https://temenos-api.example.com", "ClientId": "your-temenos-client-id", "ClientSecret": "your-temenos-client-secret", "CallbackUrl": "https://your-app.com/api/lms/webhooks"}}, {"Name": "Generic REST API Plugin", "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.GenericRestApiPlugin, FieldEZ.Infrastructure", "Enabled": true, "RequireConnection": false, "Configuration": {"BaseUrl": "https://your-lms-api.example.com", "AuthType": "bearer", "BearerToken": "your-api-token", "DateFormat": "yyyy-MM-ddTHH:mm:ssZ", "DateParameter": "lastModified", "Endpoint.Health": "/health", "Endpoint.GetLoans": "/api/v1/loans", "Endpoint.GetCustomers": "/api/v1/customers", "Endpoint.UpdateLoanStatus": "/api/v1/loans/{loanId}/status", "Endpoint.CreateCollection": "/api/v1/collections", "Endpoint.UpdatePaymentStatus": "/api/v1/payments/{paymentId}/status", "Endpoint.RegisterWebhook": "/api/v1/webhooks", "Endpoint.UnregisterWebhook": "/api/v1/webhooks/{webhookId}", "Endpoint.GetWebhooks": "/api/v1/webhooks", "LoansDataPath": "data", "CustomersDataPath": "data", "Header.Accept": "application/json", "Header.Content-Type": "application/json"}}], "FileStorage": {"Type": "Local", "LocalPath": "./uploads", "MaxFileSize": ********, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"]}, "PaymentGateways": {"Razorpay": {"KeyId": "your-razorpay-key-id", "KeySecret": "your-razorpay-key-secret", "WebhookSecret": "your-razorpay-webhook-secret"}, "UPI": {"MerchantId": "your-merchant-id", "MerchantName": "FieldEZ Collections"}}, "Notifications": {"SMS": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "your-twilio-account-sid", "AuthToken": "your-twilio-auth-token", "FromNumber": "+**********"}, "Email": {"Provider": "SendGrid", "ApiKey": "your-sendgrid-api-key", "FromEmail": "<EMAIL>", "FromName": "FieldEZ"}, "Push": {"FirebaseServerKey": "your-firebase-server-key"}}, "Maps": {"Provider": "Google", "ApiKey": "your-google-maps-api-key"}, "Analytics": {"EnableTracking": true, "RetentionDays": 90}}