using FieldEZ.Core.Interfaces;
using FieldEZ.Infrastructure.LMS;
using FieldEZ.Infrastructure.LMS.Plugins;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace FieldEZ.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddLMSServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Register LMS Plugin Manager
            services.AddSingleton<ILMSPluginManager, LMSPluginManager>();

            // Register HTTP client for plugins
            services.AddHttpClient<FinaclePlugin>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "FieldEZ/1.0");
            });

            services.AddHttpClient<TemenosPlugin>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "FieldEZ/1.0");
            });

            services.AddHttpClient<GenericRestApiPlugin>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "FieldEZ/1.0");
            });

            // Register plugin implementations
            services.AddTransient<FinaclePlugin>();
            services.AddTransient<TemenosPlugin>();
            services.AddTransient<GenericRestApiPlugin>();

            // Register plugins as ILMSPlugin
            services.AddTransient<ILMSPlugin, FinaclePlugin>();
            services.AddTransient<ILMSPlugin, TemenosPlugin>();
            services.AddTransient<ILMSPlugin, GenericRestApiPlugin>();

            return services;
        }

        public static IServiceCollection AddPaymentServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add payment gateway services
            var razorpayConfig = configuration.GetSection("PaymentGateways:Razorpay");
            if (razorpayConfig.Exists())
            {
                // Register Razorpay service
                // services.AddSingleton<IRazorpayService, RazorpayService>();
            }

            var upiConfig = configuration.GetSection("PaymentGateways:UPI");
            if (upiConfig.Exists())
            {
                // Register UPI service
                // services.AddSingleton<IUPIService, UPIService>();
            }

            return services;
        }

        public static IServiceCollection AddNotificationServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add SMS service
            var smsConfig = configuration.GetSection("Notifications:SMS");
            if (smsConfig.Exists())
            {
                // Register SMS service based on provider
                var provider = smsConfig["Provider"];
                switch (provider?.ToLower())
                {
                    case "twilio":
                        // services.AddSingleton<ISMSService, TwilioSMSService>();
                        break;
                    case "aws":
                        // services.AddSingleton<ISMSService, AWSSMSService>();
                        break;
                }
            }

            // Add email service
            var emailConfig = configuration.GetSection("Notifications:Email");
            if (emailConfig.Exists())
            {
                var provider = emailConfig["Provider"];
                switch (provider?.ToLower())
                {
                    case "sendgrid":
                        // services.AddSingleton<IEmailService, SendGridEmailService>();
                        break;
                    case "smtp":
                        // services.AddSingleton<IEmailService, SMTPEmailService>();
                        break;
                }
            }

            // Add push notification service
            var pushConfig = configuration.GetSection("Notifications:Push");
            if (pushConfig.Exists())
            {
                // services.AddSingleton<IPushNotificationService, FirebasePushService>();
            }

            return services;
        }

        public static IServiceCollection AddFileStorageServices(this IServiceCollection services, IConfiguration configuration)
        {
            var storageConfig = configuration.GetSection("FileStorage");
            var storageType = storageConfig["Type"];

            switch (storageType?.ToLower())
            {
                case "local":
                    // services.AddSingleton<IFileStorageService, LocalFileStorageService>();
                    break;
                case "aws":
                    // services.AddSingleton<IFileStorageService, AWSS3StorageService>();
                    break;
                case "azure":
                    // services.AddSingleton<IFileStorageService, AzureBlobStorageService>();
                    break;
                default:
                    // services.AddSingleton<IFileStorageService, LocalFileStorageService>();
                    break;
            }

            return services;
        }

        public static IServiceCollection AddMappingServices(this IServiceCollection services)
        {
            // Add AutoMapper or manual mapping services
            // services.AddAutoMapper(typeof(MappingProfile));
            
            return services;
        }

        public static IServiceCollection AddCachingServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add memory cache
            services.AddMemoryCache();

            // Add distributed cache (Redis)
            var redisConnection = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnection))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = redisConnection;
                });
            }
            else
            {
                // Fallback to in-memory distributed cache
                services.AddDistributedMemoryCache();
            }

            return services;
        }

        public static IServiceCollection AddBackgroundServices(this IServiceCollection services)
        {
            // Add background services for data sync, notifications, etc.
            // services.AddHostedService<LMSSyncBackgroundService>();
            // services.AddHostedService<NotificationBackgroundService>();
            // services.AddHostedService<LocationTrackingBackgroundService>();

            return services;
        }

        public static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
        {
            var healthChecks = services.AddHealthChecks();

            // Add database health check
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            if (!string.IsNullOrEmpty(connectionString))
            {
                healthChecks.AddSqlServer(connectionString, name: "database");
            }

            // Add Redis health check
            var redisConnection = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnection))
            {
                healthChecks.AddRedis(redisConnection, name: "redis");
            }

            // Add LMS plugins health check
            healthChecks.AddCheck<LMSPluginHealthCheck>("lms-plugins");

            return services;
        }
    }

    // Health check for LMS plugins
    public class LMSPluginHealthCheck : IHealthCheck
    {
        private readonly ILMSPluginManager _pluginManager;

        public LMSPluginHealthCheck(ILMSPluginManager pluginManager)
        {
            _pluginManager = pluginManager;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var healthStatuses = await _pluginManager.GetHealthStatusAsync();
                var unhealthyPlugins = healthStatuses.Where(h => !h.IsHealthy).ToList();

                if (unhealthyPlugins.Any())
                {
                    var data = unhealthyPlugins.ToDictionary(
                        h => h.Details.GetValueOrDefault("PluginName", "Unknown")?.ToString() ?? "Unknown",
                        h => h.Status
                    );

                    return HealthCheckResult.Degraded(
                        $"{unhealthyPlugins.Count} LMS plugin(s) are unhealthy",
                        data: data
                    );
                }

                return HealthCheckResult.Healthy($"{healthStatuses.Count()} LMS plugin(s) are healthy");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Failed to check LMS plugin health", ex);
            }
        }
    }
}
