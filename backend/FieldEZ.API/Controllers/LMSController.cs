using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FieldEZ.Core.Interfaces;
using FieldEZ.Core.Models;
using FieldEZ.Infrastructure.LMS;
using FieldEZ.API.DTOs;

namespace FieldEZ.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LMSController : ControllerBase
    {
        private readonly ILMSPluginManager _pluginManager;
        private readonly ILogger<LMSController> _logger;

        public LMSController(ILMSPluginManager pluginManager, ILogger<LMSController> logger)
        {
            _pluginManager = pluginManager;
            _logger = logger;
        }

        [HttpGet("plugins")]
        public ActionResult<IEnumerable<LMSPluginDto>> GetPlugins()
        {
            try
            {
                var plugins = _pluginManager.GetAllPlugins();
                var pluginDtos = plugins.Select(p => new LMSPluginDto
                {
                    Name = p.Name,
                    Version = p.Version,
                    Description = p.Description,
                    Type = p.Type.ToString(),
                    IsEnabled = p.IsEnabled
                });

                return Ok(pluginDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get LMS plugins");
                return StatusCode(500, new { message = "Failed to retrieve plugins" });
            }
        }

        [HttpGet("plugins/health")]
        public async Task<ActionResult<IEnumerable<LMSHealthStatusDto>>> GetPluginsHealth()
        {
            try
            {
                var healthStatuses = await _pluginManager.GetHealthStatusAsync();
                var healthDtos = healthStatuses.Select(h => new LMSHealthStatusDto
                {
                    PluginName = h.Details.GetValueOrDefault("PluginName", "Unknown")?.ToString() ?? "Unknown",
                    IsHealthy = h.IsHealthy,
                    Status = h.Status,
                    LastChecked = h.LastChecked,
                    ResponseTimeMs = (int)h.ResponseTime.TotalMilliseconds,
                    Details = h.Details
                });

                return Ok(healthDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get plugins health status");
                return StatusCode(500, new { message = "Failed to retrieve health status" });
            }
        }

        [HttpPost("plugins/{pluginName}/enable")]
        public async Task<ActionResult> EnablePlugin(string pluginName)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin<ILMSPlugin>(pluginName);
                if (plugin == null)
                {
                    return NotFound(new { message = "Plugin not found" });
                }

                plugin.IsEnabled = true;
                _logger.LogInformation("Plugin {PluginName} enabled", pluginName);

                return Ok(new { message = "Plugin enabled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to enable plugin {PluginName}", pluginName);
                return StatusCode(500, new { message = "Failed to enable plugin" });
            }
        }

        [HttpPost("plugins/{pluginName}/disable")]
        public async Task<ActionResult> DisablePlugin(string pluginName)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin<ILMSPlugin>(pluginName);
                if (plugin == null)
                {
                    return NotFound(new { message = "Plugin not found" });
                }

                plugin.IsEnabled = false;
                _logger.LogInformation("Plugin {PluginName} disabled", pluginName);

                return Ok(new { message = "Plugin disabled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to disable plugin {PluginName}", pluginName);
                return StatusCode(500, new { message = "Failed to disable plugin" });
            }
        }

        [HttpPost("sync")]
        public async Task<ActionResult<SyncResultDto>> SyncData([FromBody] SyncRequestDto request)
        {
            try
            {
                var syncRequest = new SyncRequest
                {
                    LastSyncDate = request.LastSyncDate,
                    EntityTypes = request.EntityTypes ?? new List<string>(),
                    Filters = request.Filters ?? new Dictionary<string, object>(),
                    BatchSize = request.BatchSize,
                    FullSync = request.FullSync
                };

                var result = await _pluginManager.SyncAllDataAsync(syncRequest);

                var resultDto = new SyncResultDto
                {
                    Success = result.Success,
                    RecordsProcessed = result.RecordsProcessed,
                    RecordsUpdated = result.RecordsUpdated,
                    RecordsCreated = result.RecordsCreated,
                    RecordsSkipped = result.RecordsSkipped,
                    SyncTimestamp = result.SyncTimestamp,
                    Errors = result.Errors.ToList(),
                    Metadata = result.Metadata
                };

                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to sync data");
                return StatusCode(500, new { message = "Data synchronization failed" });
            }
        }

        [HttpPost("webhooks")]
        public async Task<ActionResult<WebhookResponseDto>> HandleWebhook([FromBody] WebhookRequestDto request)
        {
            try
            {
                var webhookRequest = new WebhookRequest
                {
                    WebhookId = request.WebhookId,
                    EventType = Enum.Parse<LMSEventType>(request.EventType),
                    Payload = request.Payload,
                    Headers = request.Headers ?? new Dictionary<string, string>(),
                    Timestamp = request.Timestamp
                };

                // Find appropriate plugin to handle webhook
                var webhookHandlers = _pluginManager.GetPlugins<ILMSWebhookHandler>();
                
                foreach (var handler in webhookHandlers.Where(h => h.IsEnabled))
                {
                    try
                    {
                        var response = await handler.HandleWebhookAsync(webhookRequest);
                        if (response.Success)
                        {
                            return Ok(new WebhookResponseDto
                            {
                                Success = response.Success,
                                Message = response.Message,
                                StatusCode = response.StatusCode
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Webhook handler {HandlerName} failed to process webhook", handler.Name);
                    }
                }

                return BadRequest(new { message = "No handler could process the webhook" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to handle webhook");
                return StatusCode(500, new { message = "Webhook processing failed" });
            }
        }

        [HttpGet("loans")]
        public async Task<ActionResult<IEnumerable<LMSLoanDto>>> GetLoans([FromQuery] DateTime? lastSyncDate = null)
        {
            try
            {
                var dataSyncPlugins = _pluginManager.GetPlugins<ILMSDataSync>();
                var allLoans = new List<LMSLoan>();

                foreach (var plugin in dataSyncPlugins.Where(p => p.IsEnabled))
                {
                    try
                    {
                        var loans = await plugin.GetLoansAsync(lastSyncDate);
                        allLoans.AddRange(loans);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to get loans from plugin {PluginName}", plugin.Name);
                    }
                }

                var loanDtos = allLoans.Select(l => new LMSLoanDto
                {
                    LoanId = l.LoanId,
                    AccountNumber = l.AccountNumber,
                    CustomerId = l.CustomerId,
                    PrincipalAmount = l.PrincipalAmount,
                    OutstandingAmount = l.OutstandingAmount,
                    OverdueAmount = l.OverdueAmount,
                    NextDueDate = l.NextDueDate,
                    OverdueDays = l.OverdueDays,
                    InterestRate = l.InterestRate,
                    EMIAmount = l.EMIAmount,
                    Status = l.Status.ToString(),
                    CreatedDate = l.CreatedDate,
                    LastUpdated = l.LastUpdated,
                    CustomFields = l.CustomFields
                });

                return Ok(loanDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get loans from LMS");
                return StatusCode(500, new { message = "Failed to retrieve loans" });
            }
        }

        [HttpGet("customers")]
        public async Task<ActionResult<IEnumerable<LMSCustomerDto>>> GetCustomers([FromQuery] DateTime? lastSyncDate = null)
        {
            try
            {
                var dataSyncPlugins = _pluginManager.GetPlugins<ILMSDataSync>();
                var allCustomers = new List<LMSCustomer>();

                foreach (var plugin in dataSyncPlugins.Where(p => p.IsEnabled))
                {
                    try
                    {
                        var customers = await plugin.GetCustomersAsync(lastSyncDate);
                        allCustomers.AddRange(customers);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to get customers from plugin {PluginName}", plugin.Name);
                    }
                }

                var customerDtos = allCustomers.Select(c => new LMSCustomerDto
                {
                    CustomerId = c.CustomerId,
                    Name = c.Name,
                    Phone = c.Phone,
                    Email = c.Email,
                    Address = c.Address,
                    City = c.City,
                    State = c.State,
                    Pincode = c.Pincode,
                    CreatedDate = c.CreatedDate,
                    LastUpdated = c.LastUpdated,
                    CustomFields = c.CustomFields
                });

                return Ok(customerDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get customers from LMS");
                return StatusCode(500, new { message = "Failed to retrieve customers" });
            }
        }

        [HttpPost("collections")]
        public async Task<ActionResult> CreateCollectionRecord([FromBody] LMSCollectionRecordDto request)
        {
            try
            {
                var collectionRecord = new LMSCollectionRecord
                {
                    LoanId = request.LoanId,
                    AgentId = request.AgentId,
                    Amount = request.Amount,
                    Method = Enum.Parse<PaymentMethod>(request.Method),
                    CollectionDate = request.CollectionDate,
                    TransactionId = request.TransactionId,
                    ReceiptNumber = request.ReceiptNumber,
                    Notes = request.Notes,
                    CustomFields = request.CustomFields ?? new Dictionary<string, object>()
                };

                var dataSyncPlugins = _pluginManager.GetPlugins<ILMSDataSync>();
                var success = false;

                foreach (var plugin in dataSyncPlugins.Where(p => p.IsEnabled))
                {
                    try
                    {
                        var result = await plugin.CreateCollectionRecordAsync(collectionRecord);
                        if (result)
                        {
                            success = true;
                            _logger.LogInformation("Collection record created successfully via plugin {PluginName}", plugin.Name);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to create collection record via plugin {PluginName}", plugin.Name);
                    }
                }

                if (success)
                {
                    return Ok(new { message = "Collection record created successfully" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to create collection record in any LMS" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create collection record");
                return StatusCode(500, new { message = "Failed to create collection record" });
            }
        }

        [HttpPut("loans/{loanId}/status")]
        public async Task<ActionResult> UpdateLoanStatus(string loanId, [FromBody] UpdateLoanStatusRequestDto request)
        {
            try
            {
                var status = Enum.Parse<LoanStatus>(request.Status);
                var dataSyncPlugins = _pluginManager.GetPlugins<ILMSDataSync>();
                var success = false;

                foreach (var plugin in dataSyncPlugins.Where(p => p.IsEnabled))
                {
                    try
                    {
                        var result = await plugin.UpdateLoanStatusAsync(loanId, status);
                        if (result)
                        {
                            success = true;
                            _logger.LogInformation("Loan status updated successfully via plugin {PluginName}", plugin.Name);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to update loan status via plugin {PluginName}", plugin.Name);
                    }
                }

                if (success)
                {
                    return Ok(new { message = "Loan status updated successfully" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to update loan status in any LMS" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update loan status");
                return StatusCode(500, new { message = "Failed to update loan status" });
            }
        }
    }
}
