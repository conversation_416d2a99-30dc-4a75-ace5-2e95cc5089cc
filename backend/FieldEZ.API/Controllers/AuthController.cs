using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;
using FieldEZ.Infrastructure.Data;
using FieldEZ.Core.Models;
using FieldEZ.API.DTOs;

namespace FieldEZ.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly FieldEZDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            FieldEZDbContext context,
            IConfiguration configuration,
            ILogger<AuthController> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        [HttpPost("login")]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto request)
        {
            try
            {
                // Validate input
                if (string.IsNullOrEmpty(request.Phone) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { message = "Phone and password are required" });
                }

                // Find agent by phone
                var agent = await _context.Agents
                    .FirstOrDefaultAsync(a => a.Phone == request.Phone && a.Status == AgentStatus.Active);

                if (agent == null)
                {
                    return Unauthorized(new { message = "Invalid credentials" });
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(request.Password, agent.PasswordHash))
                {
                    return Unauthorized(new { message = "Invalid credentials" });
                }

                // Update last active time
                agent.LastActiveAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Generate JWT tokens
                var accessToken = GenerateAccessToken(agent);
                var refreshToken = GenerateRefreshToken();

                // Store refresh token (in production, store in database)
                // For now, we'll include it in the response

                var response = new LoginResponseDto
                {
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresIn = 3600, // 1 hour
                    Agent = new AgentDto
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        Phone = agent.Phone,
                        Email = agent.Email,
                        EmployeeId = agent.EmployeeId,
                        Role = agent.Role,
                        Status = agent.Status.ToString()
                    }
                };

                _logger.LogInformation("Agent {AgentId} logged in successfully", agent.Id);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for phone {Phone}", request.Phone);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("refresh")]
        public async Task<ActionResult<RefreshTokenResponseDto>> RefreshToken([FromBody] RefreshTokenRequestDto request)
        {
            try
            {
                // In production, validate refresh token from database
                // For now, we'll generate a new access token
                
                var principal = GetPrincipalFromExpiredToken(request.RefreshToken);
                if (principal == null)
                {
                    return Unauthorized(new { message = "Invalid refresh token" });
                }

                var agentIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(agentIdClaim, out var agentId))
                {
                    return Unauthorized(new { message = "Invalid token claims" });
                }

                var agent = await _context.Agents
                    .FirstOrDefaultAsync(a => a.Id == agentId && a.Status == AgentStatus.Active);

                if (agent == null)
                {
                    return Unauthorized(new { message = "Agent not found or inactive" });
                }

                var newAccessToken = GenerateAccessToken(agent);

                var response = new RefreshTokenResponseDto
                {
                    AccessToken = newAccessToken,
                    ExpiresIn = 3600
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public async Task<ActionResult> Logout()
        {
            try
            {
                var agentIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(agentIdClaim, out var agentId))
                {
                    var agent = await _context.Agents.FindAsync(agentId);
                    if (agent != null)
                    {
                        // In production, invalidate refresh tokens in database
                        _logger.LogInformation("Agent {AgentId} logged out", agentId);
                    }
                }

                return Ok(new { message = "Logged out successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("register")]
        public async Task<ActionResult<AgentDto>> Register([FromBody] RegisterRequestDto request)
        {
            try
            {
                // Validate input
                if (string.IsNullOrEmpty(request.Name) || 
                    string.IsNullOrEmpty(request.Phone) || 
                    string.IsNullOrEmpty(request.Password) ||
                    string.IsNullOrEmpty(request.EmployeeId))
                {
                    return BadRequest(new { message = "All required fields must be provided" });
                }

                // Check if agent already exists
                var existingAgent = await _context.Agents
                    .AnyAsync(a => a.Phone == request.Phone || a.EmployeeId == request.EmployeeId);

                if (existingAgent)
                {
                    return Conflict(new { message = "Agent with this phone or employee ID already exists" });
                }

                // Create new agent
                var agent = new Agent
                {
                    Name = request.Name,
                    Phone = request.Phone,
                    Email = request.Email,
                    EmployeeId = request.EmployeeId,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                    Role = request.Role ?? "FieldAgent",
                    Status = AgentStatus.Active
                };

                _context.Agents.Add(agent);
                await _context.SaveChangesAsync();

                var response = new AgentDto
                {
                    Id = agent.Id,
                    Name = agent.Name,
                    Phone = agent.Phone,
                    Email = agent.Email,
                    EmployeeId = agent.EmployeeId,
                    Role = agent.Role,
                    Status = agent.Status.ToString()
                };

                _logger.LogInformation("New agent registered: {AgentId}", agent.Id);
                return CreatedAtAction(nameof(Register), new { id = agent.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during agent registration");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        private string GenerateAccessToken(Agent agent)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Secret"] ?? "your-secret-key-here");
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, agent.Id.ToString()),
                    new Claim(ClaimTypes.Name, agent.Name),
                    new Claim(ClaimTypes.MobilePhone, agent.Phone),
                    new Claim(ClaimTypes.Role, agent.Role),
                    new Claim("employee_id", agent.EmployeeId)
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature),
                Issuer = _configuration["Jwt:Issuer"],
                Audience = _configuration["Jwt:Audience"]
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }

        private ClaimsPrincipal? GetPrincipalFromExpiredToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(
                    Encoding.ASCII.GetBytes(_configuration["Jwt:Secret"] ?? "your-secret-key-here")),
                ValidateLifetime = false // We don't care about expiration for refresh
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            try
            {
                var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);
                if (securityToken is not JwtSecurityToken jwtSecurityToken ||
                    !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    return null;
                }

                return principal;
            }
            catch
            {
                return null;
            }
        }
    }
}
