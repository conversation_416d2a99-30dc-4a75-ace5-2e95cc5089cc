using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using FieldEZ.Infrastructure.Data;
using FieldEZ.Core.Models;
using FieldEZ.API.DTOs;
using NetTopologySuite.Geometries;

namespace FieldEZ.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LeadsController : ControllerBase
    {
        private readonly FieldEZDbContext _context;
        private readonly ILogger<LeadsController> _logger;

        public LeadsController(FieldEZDbContext context, ILogger<LeadsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<LeadDto>>> GetLeads(
            [FromQuery] string? status = null,
            [FromQuery] DateTime? date = null,
            [FromQuery] int limit = 50,
            [FromQuery] int offset = 0)
        {
            try
            {
                var agentId = GetCurrentAgentId();
                if (agentId == null)
                {
                    return Unauthorized();
                }

                var query = _context.Leads
                    .Include(l => l.Customer)
                    .Include(l => l.Loan)
                    .Include(l => l.Visits.OrderByDescending(v => v.VisitDate).Take(5))
                    .Include(l => l.Payments.Where(p => p.Status == PaymentStatus.Success).OrderByDescending(p => p.PaymentTimestamp).Take(5))
                    .Where(l => l.AssignedAgentId == agentId);

                // Apply filters
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<LeadStatus>(status, true, out var leadStatus))
                {
                    query = query.Where(l => l.Status == leadStatus);
                }

                if (date.HasValue)
                {
                    var startDate = date.Value.Date;
                    var endDate = startDate.AddDays(1);
                    query = query.Where(l => l.AssignedAt >= startDate && l.AssignedAt < endDate);
                }

                var totalCount = await query.CountAsync();
                var leads = await query
                    .OrderByDescending(l => l.Priority)
                    .ThenBy(l => l.Deadline)
                    .Skip(offset)
                    .Take(limit)
                    .ToListAsync();

                var leadDtos = leads.Select(MapToLeadDto).ToList();

                Response.Headers.Add("X-Total-Count", totalCount.ToString());
                Response.Headers.Add("X-Has-More", (offset + limit < totalCount).ToString());

                return Ok(leadDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving leads for agent {AgentId}", GetCurrentAgentId());
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<LeadDto>> GetLead(Guid id)
        {
            try
            {
                var agentId = GetCurrentAgentId();
                if (agentId == null)
                {
                    return Unauthorized();
                }

                var lead = await _context.Leads
                    .Include(l => l.Customer)
                    .Include(l => l.Loan)
                    .Include(l => l.Visits.OrderByDescending(v => v.VisitDate))
                    .Include(l => l.Payments.Where(p => p.Status == PaymentStatus.Success).OrderByDescending(p => p.PaymentTimestamp))
                    .FirstOrDefaultAsync(l => l.Id == id && l.AssignedAgentId == agentId);

                if (lead == null)
                {
                    return NotFound(new { message = "Lead not found" });
                }

                var leadDto = MapToLeadDto(lead);
                return Ok(leadDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lead {LeadId}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("{id}/visit")]
        public async Task<ActionResult<VisitDto>> CreateVisit(Guid id, [FromBody] CreateVisitRequestDto request)
        {
            try
            {
                var agentId = GetCurrentAgentId();
                if (agentId == null)
                {
                    return Unauthorized();
                }

                var lead = await _context.Leads
                    .FirstOrDefaultAsync(l => l.Id == id && l.AssignedAgentId == agentId);

                if (lead == null)
                {
                    return NotFound(new { message = "Lead not found" });
                }

                // Create visit record
                var visit = new Visit
                {
                    LeadId = id,
                    AgentId = agentId.Value,
                    VisitDate = request.Timestamp,
                    Result = Enum.Parse<VisitResult>(request.Result, true),
                    Notes = request.Notes,
                    NextVisitScheduled = request.NextVisitScheduled,
                    FollowUpNotes = request.FollowUpNotes,
                    CheckInTime = request.Timestamp,
                    CheckOutTime = DateTime.UtcNow
                };

                // Set location if provided
                if (request.Location != null)
                {
                    var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
                    visit.Location = geometryFactory.CreatePoint(new Coordinate(request.Location.Longitude, request.Location.Latitude));
                }

                // Calculate duration
                if (visit.CheckInTime.HasValue && visit.CheckOutTime.HasValue)
                {
                    visit.DurationMinutes = (int)(visit.CheckOutTime.Value - visit.CheckInTime.Value).TotalMinutes;
                }

                _context.Visits.Add(visit);

                // Update lead statistics
                lead.VisitCount++;
                lead.LastVisitDate = request.Timestamp;

                if (visit.Result == VisitResult.PaymentCollected || visit.Result == VisitResult.PartialPayment)
                {
                    lead.SuccessfulVisits++;
                }

                // Update lead status based on visit result
                switch (visit.Result)
                {
                    case VisitResult.PaymentCollected:
                        if (lead.TotalCollected >= (lead.ExpectedCollectionAmount ?? 0))
                        {
                            lead.Status = LeadStatus.Collected;
                        }
                        else
                        {
                            lead.Status = LeadStatus.Visited;
                        }
                        break;
                    case VisitResult.PartialPayment:
                        lead.Status = LeadStatus.Visited;
                        break;
                    case VisitResult.RefusedToPay:
                        lead.Status = LeadStatus.Failed;
                        break;
                    default:
                        lead.Status = LeadStatus.Visited;
                        break;
                }

                await _context.SaveChangesAsync();

                var visitDto = MapToVisitDto(visit);

                _logger.LogInformation("Visit created for lead {LeadId} by agent {AgentId}", id, agentId);
                return CreatedAtAction(nameof(CreateVisit), new { id = visit.Id }, visitDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating visit for lead {LeadId}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPut("{id}/status")]
        public async Task<ActionResult> UpdateLeadStatus(Guid id, [FromBody] UpdateLeadStatusRequestDto request)
        {
            try
            {
                var agentId = GetCurrentAgentId();
                if (agentId == null)
                {
                    return Unauthorized();
                }

                var lead = await _context.Leads
                    .FirstOrDefaultAsync(l => l.Id == id && l.AssignedAgentId == agentId);

                if (lead == null)
                {
                    return NotFound(new { message = "Lead not found" });
                }

                if (Enum.TryParse<LeadStatus>(request.Status, true, out var newStatus))
                {
                    lead.Status = newStatus;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Lead {LeadId} status updated to {Status} by agent {AgentId}", id, newStatus, agentId);
                    return Ok(new { message = "Lead status updated successfully" });
                }

                return BadRequest(new { message = "Invalid status value" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lead status for lead {LeadId}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        private Guid? GetCurrentAgentId()
        {
            var agentIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(agentIdClaim, out var agentId) ? agentId : null;
        }

        private LeadDto MapToLeadDto(Lead lead)
        {
            return new LeadDto
            {
                Id = lead.Id,
                Customer = new CustomerDto
                {
                    Id = lead.Customer.Id,
                    Name = lead.Customer.Name,
                    Phone = lead.Customer.Phone,
                    Email = lead.Customer.Email,
                    Address = new AddressDto
                    {
                        AddressLine1 = lead.Customer.AddressLine1,
                        AddressLine2 = lead.Customer.AddressLine2,
                        City = lead.Customer.City,
                        State = lead.Customer.State,
                        Pincode = lead.Customer.Pincode,
                        Country = lead.Customer.Country,
                        Coordinates = lead.Customer.Coordinates != null ? new LocationDto
                        {
                            Latitude = lead.Customer.Coordinates.Y,
                            Longitude = lead.Customer.Coordinates.X,
                            Timestamp = DateTime.UtcNow
                        } : null
                    }
                },
                Loan = new LoanDto
                {
                    Id = lead.Loan.Id,
                    AccountNumber = lead.Loan.AccountNumber,
                    PrincipalAmount = lead.Loan.PrincipalAmount,
                    OutstandingAmount = lead.Loan.OutstandingAmount,
                    OverdueAmount = lead.Loan.OverdueAmount,
                    NextDueDate = lead.Loan.NextDueDate,
                    OverdueDays = lead.Loan.OverdueDays,
                    InterestRate = lead.Loan.InterestRate,
                    EMIAmount = lead.Loan.EMIAmount
                },
                Assignment = new AssignmentDto
                {
                    AssignedAt = lead.AssignedAt,
                    Priority = lead.Priority.ToString(),
                    ExpectedCollectionAmount = lead.ExpectedCollectionAmount,
                    Deadline = lead.Deadline,
                    VisitCount = lead.VisitCount,
                    SuccessfulVisits = lead.SuccessfulVisits,
                    TotalCollected = lead.TotalCollected
                },
                Status = lead.Status.ToString(),
                VisitHistory = lead.Visits.Select(MapToVisitDto).ToList(),
                PaymentHistory = lead.Payments.Select(MapToPaymentDto).ToList()
            };
        }

        private VisitDto MapToVisitDto(Visit visit)
        {
            return new VisitDto
            {
                Id = visit.Id,
                VisitDate = visit.VisitDate,
                Result = visit.Result.ToString(),
                Notes = visit.Notes,
                Location = visit.Location != null ? new LocationDto
                {
                    Latitude = visit.Location.Y,
                    Longitude = visit.Location.X,
                    Timestamp = visit.VisitDate
                } : null,
                DurationMinutes = visit.DurationMinutes,
                NextVisitScheduled = visit.NextVisitScheduled,
                FollowUpNotes = visit.FollowUpNotes
            };
        }

        private PaymentDto MapToPaymentDto(Payment payment)
        {
            return new PaymentDto
            {
                Id = payment.Id,
                Amount = payment.Amount,
                Method = payment.Method.ToString(),
                Status = payment.Status.ToString(),
                PaymentTimestamp = payment.PaymentTimestamp,
                TransactionId = payment.TransactionId,
                ReceiptNumber = payment.ReceiptNumber,
                ReceiptUrl = payment.ReceiptUrl,
                PaymentLocation = payment.PaymentLocation != null ? new LocationDto
                {
                    Latitude = payment.PaymentLocation.Y,
                    Longitude = payment.PaymentLocation.X,
                    Timestamp = payment.PaymentTimestamp
                } : null
            };
        }
    }

    public class UpdateLeadStatusRequestDto
    {
        [Required]
        public string Status { get; set; } = string.Empty;
    }
}
