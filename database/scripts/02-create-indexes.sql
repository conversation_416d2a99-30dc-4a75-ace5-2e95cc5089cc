-- FieldEZ Loan Collection System - Indexes and Views
-- SQL Server performance optimization

USE FieldEZ;
GO

-- =============================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =============================================

-- Agents table indexes
CREATE NONCLUSTERED INDEX IX_Agents_Status ON Agents(Status);
CREATE NONCLUSTERED INDEX IX_Agents_Phone ON Agents(Phone);
CREATE NONCLUSTERED INDEX IX_Agents_EmployeeId ON Agents(EmployeeId);
CREATE NONCLUSTERED INDEX IX_Agents_LastActiveAt ON Agents(LastActiveAt);

-- Spatial index for agent locations
CREATE SPATIAL INDEX SIX_Agents_CurrentLocation ON Agents(CurrentLocation)
USING GEOGRAPHY_GRID
WITH (GRIDS = (LEVEL_1 = MEDIUM, LEVEL_2 = MEDIUM, LEVEL_3 = MEDIUM, LEVEL_4 = MEDIUM));

-- Customers table indexes
CREATE NONCLUSTERED INDEX IX_Customers_Phone ON Customers(Phone);
CREATE NONCLUSTERED INDEX IX_Customers_Name ON Customers(Name);
CREATE NONCLUSTERED INDEX IX_Customers_City ON Customers(City);

-- Spatial index for customer coordinates
CREATE SPATIAL INDEX SIX_Customers_Coordinates ON Customers(Coordinates)
USING GEOGRAPHY_GRID
WITH (GRIDS = (LEVEL_1 = MEDIUM, LEVEL_2 = MEDIUM, LEVEL_3 = MEDIUM, LEVEL_4 = MEDIUM));

-- Loans table indexes
CREATE NONCLUSTERED INDEX IX_Loans_CustomerId ON Loans(CustomerId);
CREATE NONCLUSTERED INDEX IX_Loans_AccountNumber ON Loans(AccountNumber);
CREATE NONCLUSTERED INDEX IX_Loans_NextDueDate ON Loans(NextDueDate);
CREATE NONCLUSTERED INDEX IX_Loans_OverdueDays ON Loans(OverdueDays) WHERE OverdueDays > 0;
CREATE NONCLUSTERED INDEX IX_Loans_OutstandingAmount ON Loans(OutstandingAmount);

-- Leads table indexes
CREATE NONCLUSTERED INDEX IX_Leads_AssignedAgentId ON Leads(AssignedAgentId);
CREATE NONCLUSTERED INDEX IX_Leads_Status ON Leads(Status);
CREATE NONCLUSTERED INDEX IX_Leads_Priority ON Leads(Priority);
CREATE NONCLUSTERED INDEX IX_Leads_Deadline ON Leads(Deadline);
CREATE NONCLUSTERED INDEX IX_Leads_CustomerId ON Leads(CustomerId);
CREATE NONCLUSTERED INDEX IX_Leads_LoanId ON Leads(LoanId);
CREATE NONCLUSTERED INDEX IX_Leads_AssignedAt ON Leads(AssignedAt);

-- Composite index for active leads
CREATE NONCLUSTERED INDEX IX_Leads_Agent_Status_Priority ON Leads(AssignedAgentId, Status, Priority);

-- Visits table indexes
CREATE NONCLUSTERED INDEX IX_Visits_LeadId ON Visits(LeadId);
CREATE NONCLUSTERED INDEX IX_Visits_AgentId ON Visits(AgentId);
CREATE NONCLUSTERED INDEX IX_Visits_VisitDate ON Visits(VisitDate);
CREATE NONCLUSTERED INDEX IX_Visits_Result ON Visits(Result);

-- Spatial index for visit locations
CREATE SPATIAL INDEX SIX_Visits_Location ON Visits(Location)
USING GEOGRAPHY_GRID
WITH (GRIDS = (LEVEL_1 = MEDIUM, LEVEL_2 = MEDIUM, LEVEL_3 = MEDIUM, LEVEL_4 = MEDIUM));

-- Payments table indexes
CREATE NONCLUSTERED INDEX IX_Payments_LeadId ON Payments(LeadId);
CREATE NONCLUSTERED INDEX IX_Payments_AgentId ON Payments(AgentId);
CREATE NONCLUSTERED INDEX IX_Payments_Status ON Payments(Status);
CREATE NONCLUSTERED INDEX IX_Payments_PaymentTimestamp ON Payments(PaymentTimestamp);
CREATE NONCLUSTERED INDEX IX_Payments_Method ON Payments(Method);
CREATE NONCLUSTERED INDEX IX_Payments_ReceiptNumber ON Payments(ReceiptNumber);
CREATE NONCLUSTERED INDEX IX_Payments_TransactionId ON Payments(TransactionId);

-- Composite index for successful payments
CREATE NONCLUSTERED INDEX IX_Payments_Agent_Status_Date ON Payments(AgentId, Status, PaymentTimestamp);

-- Documents table indexes
CREATE NONCLUSTERED INDEX IX_Documents_LeadId ON Documents(LeadId);
CREATE NONCLUSTERED INDEX IX_Documents_VisitId ON Documents(VisitId);
CREATE NONCLUSTERED INDEX IX_Documents_PaymentId ON Documents(PaymentId);
CREATE NONCLUSTERED INDEX IX_Documents_AgentId ON Documents(AgentId);
CREATE NONCLUSTERED INDEX IX_Documents_DocumentType ON Documents(DocumentType);

-- AgentLocations table indexes
CREATE NONCLUSTERED INDEX IX_AgentLocations_AgentId ON AgentLocations(AgentId);
CREATE NONCLUSTERED INDEX IX_AgentLocations_Timestamp ON AgentLocations(Timestamp);

-- Spatial index for agent location tracking
CREATE SPATIAL INDEX SIX_AgentLocations_Location ON AgentLocations(Location)
USING GEOGRAPHY_GRID
WITH (GRIDS = (LEVEL_1 = MEDIUM, LEVEL_2 = MEDIUM, LEVEL_3 = MEDIUM, LEVEL_4 = MEDIUM));

-- Notifications table indexes
CREATE NONCLUSTERED INDEX IX_Notifications_AgentId ON Notifications(AgentId);
CREATE NONCLUSTERED INDEX IX_Notifications_IsRead ON Notifications(IsRead);
CREATE NONCLUSTERED INDEX IX_Notifications_Type ON Notifications(Type);
CREATE NONCLUSTERED INDEX IX_Notifications_Priority ON Notifications(Priority);

-- SyncLogs table indexes
CREATE NONCLUSTERED INDEX IX_SyncLogs_AgentId ON SyncLogs(AgentId);
CREATE NONCLUSTERED INDEX IX_SyncLogs_Status ON SyncLogs(Status);
CREATE NONCLUSTERED INDEX IX_SyncLogs_StartedAt ON SyncLogs(StartedAt);

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- Agent Performance View
CREATE VIEW vw_AgentPerformance AS
SELECT 
    a.Id,
    a.Name,
    a.Phone,
    a.EmployeeId,
    a.Status,
    COUNT(DISTINCT l.Id) as TotalLeads,
    COUNT(DISTINCT v.Id) as TotalVisits,
    COUNT(DISTINCT p.Id) as TotalPayments,
    ISNULL(SUM(CASE WHEN p.Status = 'Success' THEN p.Amount ELSE 0 END), 0) as TotalCollected,
    CASE 
        WHEN COUNT(DISTINCT l.Id) > 0 
        THEN CAST(COUNT(DISTINCT CASE WHEN l.Status = 'Collected' THEN l.Id END) * 100.0 / COUNT(DISTINCT l.Id) AS DECIMAL(5,2))
        ELSE 0 
    END as SuccessRate,
    CASE 
        WHEN COUNT(DISTINCT v.Id) > 0 
        THEN AVG(CAST(v.DurationMinutes AS FLOAT))
        ELSE 0 
    END as AvgVisitDurationMinutes
FROM Agents a
LEFT JOIN Leads l ON a.Id = l.AssignedAgentId
LEFT JOIN Visits v ON a.Id = v.AgentId
LEFT JOIN Payments p ON a.Id = p.AgentId AND p.Status = 'Success'
GROUP BY a.Id, a.Name, a.Phone, a.EmployeeId, a.Status;
GO

-- Overdue Leads View
CREATE VIEW vw_OverdueLeads AS
SELECT 
    l.Id as LeadId,
    l.Priority,
    l.AssignedAgentId,
    l.Status,
    c.Name as CustomerName,
    c.Phone as CustomerPhone,
    c.AddressLine1,
    c.City,
    c.State,
    ln.AccountNumber,
    ln.OverdueAmount,
    ln.OverdueDays,
    l.ExpectedCollectionAmount,
    l.Deadline,
    l.LastVisitDate,
    l.VisitCount,
    a.Name as AgentName,
    a.Phone as AgentPhone
FROM Leads l
INNER JOIN Customers c ON l.CustomerId = c.Id
INNER JOIN Loans ln ON l.LoanId = ln.Id
LEFT JOIN Agents a ON l.AssignedAgentId = a.Id
WHERE l.Status IN ('Assigned', 'Visited') 
AND ln.OverdueDays > 0;
GO

-- Daily Collections Summary View
CREATE VIEW vw_DailyCollectionsSummary AS
SELECT 
    CAST(p.PaymentTimestamp AS DATE) as CollectionDate,
    a.Id as AgentId,
    a.Name as AgentName,
    COUNT(p.Id) as PaymentCount,
    SUM(CASE WHEN p.Status = 'Success' THEN p.Amount ELSE 0 END) as TotalAmount,
    COUNT(CASE WHEN p.Method = 'Cash' THEN 1 END) as CashPayments,
    COUNT(CASE WHEN p.Method = 'UPI' THEN 1 END) as UPIPayments,
    COUNT(CASE WHEN p.Method = 'Card' THEN 1 END) as CardPayments,
    SUM(CASE WHEN p.Method = 'Cash' AND p.Status = 'Success' THEN p.Amount ELSE 0 END) as CashAmount,
    SUM(CASE WHEN p.Method = 'UPI' AND p.Status = 'Success' THEN p.Amount ELSE 0 END) as UPIAmount,
    SUM(CASE WHEN p.Method = 'Card' AND p.Status = 'Success' THEN p.Amount ELSE 0 END) as CardAmount
FROM Payments p
INNER JOIN Agents a ON p.AgentId = a.Id
WHERE p.PaymentTimestamp >= DATEADD(day, -30, GETUTCDATE()) -- Last 30 days
GROUP BY CAST(p.PaymentTimestamp AS DATE), a.Id, a.Name;
GO

-- Lead Assignment Summary View
CREATE VIEW vw_LeadAssignmentSummary AS
SELECT 
    a.Id as AgentId,
    a.Name as AgentName,
    a.Status as AgentStatus,
    COUNT(l.Id) as TotalAssignedLeads,
    COUNT(CASE WHEN l.Status = 'Assigned' THEN 1 END) as PendingLeads,
    COUNT(CASE WHEN l.Status = 'Visited' THEN 1 END) as VisitedLeads,
    COUNT(CASE WHEN l.Status = 'Collected' THEN 1 END) as CollectedLeads,
    COUNT(CASE WHEN l.Status = 'Failed' THEN 1 END) as FailedLeads,
    COUNT(CASE WHEN l.Priority = 'High' OR l.Priority = 'Urgent' THEN 1 END) as HighPriorityLeads,
    SUM(l.ExpectedCollectionAmount) as TotalExpectedAmount,
    SUM(l.TotalCollected) as TotalCollectedAmount
FROM Agents a
LEFT JOIN Leads l ON a.Id = l.AssignedAgentId
WHERE a.Status = 'Active'
GROUP BY a.Id, a.Name, a.Status;
GO

-- Payment Method Performance View
CREATE VIEW vw_PaymentMethodPerformance AS
SELECT 
    p.Method,
    COUNT(p.Id) as TotalTransactions,
    COUNT(CASE WHEN p.Status = 'Success' THEN 1 END) as SuccessfulTransactions,
    COUNT(CASE WHEN p.Status = 'Failed' THEN 1 END) as FailedTransactions,
    CAST(COUNT(CASE WHEN p.Status = 'Success' THEN 1 END) * 100.0 / COUNT(p.Id) AS DECIMAL(5,2)) as SuccessRate,
    SUM(CASE WHEN p.Status = 'Success' THEN p.Amount ELSE 0 END) as TotalSuccessfulAmount,
    AVG(CASE WHEN p.Status = 'Success' THEN p.Amount END) as AvgTransactionAmount
FROM Payments p
WHERE p.PaymentTimestamp >= DATEADD(day, -30, GETUTCDATE()) -- Last 30 days
GROUP BY p.Method;
GO

-- Recent Activity View
CREATE VIEW vw_RecentActivity AS
SELECT 
    'Visit' as ActivityType,
    v.Id as ActivityId,
    v.AgentId,
    a.Name as AgentName,
    v.LeadId as RelatedLeadId,
    c.Name as CustomerName,
    v.VisitDate as ActivityTimestamp,
    v.Result as ActivityDetails,
    v.Notes as ActivityNotes
FROM Visits v
INNER JOIN Agents a ON v.AgentId = a.Id
INNER JOIN Leads l ON v.LeadId = l.Id
INNER JOIN Customers c ON l.CustomerId = c.Id
WHERE v.VisitDate >= DATEADD(hour, -24, GETUTCDATE())

UNION ALL

SELECT 
    'Payment' as ActivityType,
    p.Id as ActivityId,
    p.AgentId,
    a.Name as AgentName,
    p.LeadId as RelatedLeadId,
    c.Name as CustomerName,
    p.PaymentTimestamp as ActivityTimestamp,
    CONCAT(p.Method, ' - ₹', FORMAT(p.Amount, 'N2'), ' - ', p.Status) as ActivityDetails,
    p.TransactionId as ActivityNotes
FROM Payments p
INNER JOIN Agents a ON p.AgentId = a.Id
INNER JOIN Leads l ON p.LeadId = l.Id
INNER JOIN Customers c ON l.CustomerId = c.Id
WHERE p.PaymentTimestamp >= DATEADD(hour, -24, GETUTCDATE());
GO

-- =============================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =============================================

-- Get Agent Dashboard Data
CREATE PROCEDURE sp_GetAgentDashboard
    @AgentId UNIQUEIDENTIFIER,
    @Date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @Date IS NULL
        SET @Date = CAST(GETUTCDATE() AS DATE);
    
    -- Today's statistics
    SELECT 
        COUNT(CASE WHEN l.Status = 'Assigned' THEN 1 END) as PendingLeads,
        COUNT(CASE WHEN l.Status = 'Visited' THEN 1 END) as VisitedLeads,
        COUNT(CASE WHEN l.Status = 'Collected' THEN 1 END) as CollectedLeads,
        COUNT(CASE WHEN v.VisitDate >= @Date AND v.VisitDate < DATEADD(day, 1, @Date) THEN 1 END) as TodaysVisits,
        COUNT(CASE WHEN p.PaymentTimestamp >= @Date AND p.PaymentTimestamp < DATEADD(day, 1, @Date) AND p.Status = 'Success' THEN 1 END) as TodaysPayments,
        ISNULL(SUM(CASE WHEN p.PaymentTimestamp >= @Date AND p.PaymentTimestamp < DATEADD(day, 1, @Date) AND p.Status = 'Success' THEN p.Amount ELSE 0 END), 0) as TodaysCollection
    FROM Leads l
    LEFT JOIN Visits v ON l.Id = v.LeadId
    LEFT JOIN Payments p ON l.Id = p.LeadId
    WHERE l.AssignedAgentId = @AgentId;
END
GO

PRINT 'Indexes, views, and stored procedures created successfully!';
