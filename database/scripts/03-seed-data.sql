-- FieldEZ Loan Collection System - Seed Data
-- Demo data for testing and development

USE FieldEZ;
GO

-- Clear existing data (for development only)
DELETE FROM SyncLogs;
DELETE FROM Notifications;
DELETE FROM AgentLocations;
DELETE FROM Documents;
DELETE FROM Payments;
DELETE FROM Visits;
DELETE FROM Leads;
DELETE FROM Loans;
DELETE FROM Customers;
DELETE FROM Agents;
GO

-- Insert Demo Agents
INSERT INTO Agents (EmployeeId, Name, Phone, Email, PasswordHash, Status, Role, 
                   TotalCollections, TotalAmountCollected, SuccessRate, AvgCollectionTimeMinutes,
                   CreatedAt, UpdatedAt, LastActiveAt)
VALUES 
('EMP001', '<PERSON><PERSON>', '+919876543210', '<EMAIL>', 
 '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
 'Active', 'FieldAgent', 25, 125000.00, 85.50, 45, GETUTCDATE(), GETUTCDATE(), GETUTCDATE()),

('EMP002', '<PERSON>riya <PERSON>', '+919876543211', '<EMAIL>',
 '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
 'Active', 'FieldAgent', 18, 95000.00, 78.20, 52, GETUTCDATE(), GETUTCDATE(), GETUTCDATE()),

('EMP003', 'Amit Patel', '+919876543212', '<EMAIL>',
 '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
 'Active', 'FieldAgent', 32, 180000.00, 92.30, 38, GETUTCDATE(), GETUTCDATE(), GETUTCDATE()),

('MGR001', 'Sunita Reddy', '+919876543213', '<EMAIL>',
 '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
 'Active', 'Manager', 0, 0.00, 0.00, 0, GETUTCDATE(), GETUTCDATE(), GETUTCDATE());
GO

-- Insert Demo Customers
INSERT INTO Customers (Name, Phone, Email, AddressLine1, AddressLine2, City, State, Pincode, Country,
                      DateOfBirth, Occupation, AnnualIncome, CreatedAt, UpdatedAt)
VALUES 
('Ramesh Gupta', '+************', '<EMAIL>', 
 '123 MG Road', 'Near City Mall', 'Mumbai', 'Maharashtra', '400001', 'India',
 '1985-06-15', 'Software Engineer', 800000.00, GETUTCDATE(), GETUTCDATE()),

('Kavita Singh', '+************', '<EMAIL>',
 '456 Park Street', 'Sector 15', 'Pune', 'Maharashtra', '411001', 'India',
 '1990-03-22', 'Teacher', 450000.00, GETUTCDATE(), GETUTCDATE()),

('Suresh Yadav', '+************', '<EMAIL>',
 '789 Gandhi Nagar', 'Block A', 'Delhi', 'Delhi', '110001', 'India',
 '1982-11-08', 'Business Owner', 1200000.00, GETUTCDATE(), GETUTCDATE()),

('Meera Joshi', '+************', '<EMAIL>',
 '321 Lake View', 'Phase 2', 'Bangalore', 'Karnataka', '560001', 'India',
 '1988-09-12', 'Marketing Manager', 650000.00, GETUTCDATE(), GETUTCDATE()),

('Vikram Mehta', '+************', '<EMAIL>',
 '654 Ring Road', 'Satellite', 'Ahmedabad', 'Gujarat', '380001', 'India',
 '1975-12-03', 'Doctor', 1500000.00, GETUTCDATE(), GETUTCDATE());
GO

-- Insert Demo Loans
DECLARE @Customer1 UNIQUEIDENTIFIER = (SELECT Id FROM Customers WHERE Phone = '+************');
DECLARE @Customer2 UNIQUEIDENTIFIER = (SELECT Id FROM Customers WHERE Phone = '+************');
DECLARE @Customer3 UNIQUEIDENTIFIER = (SELECT Id FROM Customers WHERE Phone = '+************');
DECLARE @Customer4 UNIQUEIDENTIFIER = (SELECT Id FROM Customers WHERE Phone = '+************');
DECLARE @Customer5 UNIQUEIDENTIFIER = (SELECT Id FROM Customers WHERE Phone = '+************');

INSERT INTO Loans (AccountNumber, CustomerId, PrincipalAmount, InterestRate, TenureMonths, EMIAmount,
                  OutstandingAmount, OverdueAmount, LastPaymentDate, NextDueDate, OverdueDays,
                  DisbursedAt, MaturityDate, CreatedAt, UpdatedAt)
VALUES 
('LOAN001', @Customer1, 500000.00, 12.50, 60, 11122.00, 
 350000.00, 25000.00, DATEADD(month, -2, GETDATE()), DATEADD(day, -15, GETDATE()), 15,
 DATEADD(month, -18, GETDATE()), DATEADD(month, 42, GETDATE()), GETUTCDATE(), GETUTCDATE()),

('LOAN002', @Customer2, 300000.00, 11.75, 36, 10456.00,
 180000.00, 15000.00, DATEADD(month, -1, GETDATE()), DATEADD(day, -8, GETDATE()), 8,
 DATEADD(month, -12, GETDATE()), DATEADD(month, 24, GETDATE()), GETUTCDATE(), GETUTCDATE()),

('LOAN003', @Customer3, 750000.00, 13.25, 84, 12890.00,
 620000.00, 45000.00, DATEADD(month, -3, GETDATE()), DATEADD(day, -22, GETDATE()), 22,
 DATEADD(month, -24, GETDATE()), DATEADD(month, 60, GETDATE()), GETUTCDATE(), GETUTCDATE()),

('LOAN004', @Customer4, 400000.00, 12.00, 48, 10526.00,
 280000.00, 8000.00, DATEADD(day, -25, GETDATE()), DATEADD(day, -5, GETDATE()), 5,
 DATEADD(month, -15, GETDATE()), DATEADD(month, 33, GETDATE()), GETUTCDATE(), GETUTCDATE()),

('LOAN005', @Customer5, 1000000.00, 11.50, 72, 17708.00,
 850000.00, 0.00, DATEADD(day, -10, GETDATE()), DATEADD(day, 20, GETDATE()), 0,
 DATEADD(month, -8, GETDATE()), DATEADD(month, 64, GETDATE()), GETUTCDATE(), GETUTCDATE());
GO

-- Insert Demo Leads
DECLARE @Agent1 UNIQUEIDENTIFIER = (SELECT Id FROM Agents WHERE EmployeeId = 'EMP001');
DECLARE @Agent2 UNIQUEIDENTIFIER = (SELECT Id FROM Agents WHERE EmployeeId = 'EMP002');
DECLARE @Agent3 UNIQUEIDENTIFIER = (SELECT Id FROM Agents WHERE EmployeeId = 'EMP003');

DECLARE @Loan1 UNIQUEIDENTIFIER = (SELECT Id FROM Loans WHERE AccountNumber = 'LOAN001');
DECLARE @Loan2 UNIQUEIDENTIFIER = (SELECT Id FROM Loans WHERE AccountNumber = 'LOAN002');
DECLARE @Loan3 UNIQUEIDENTIFIER = (SELECT Id FROM Loans WHERE AccountNumber = 'LOAN003');
DECLARE @Loan4 UNIQUEIDENTIFIER = (SELECT Id FROM Loans WHERE AccountNumber = 'LOAN004');
DECLARE @Loan5 UNIQUEIDENTIFIER = (SELECT Id FROM Loans WHERE AccountNumber = 'LOAN005');

INSERT INTO Leads (LoanId, CustomerId, AssignedAgentId, AssignedAt, Priority, ExpectedCollectionAmount,
                  Deadline, Status, LastVisitDate, VisitCount, SuccessfulVisits, TotalCollected,
                  LastCollectionDate, CreatedAt, UpdatedAt)
VALUES 
(@Loan1, @Customer1, @Agent1, DATEADD(day, -10, GETDATE()), 'High', 25000.00,
 DATEADD(day, 5, GETDATE()), 'Assigned', NULL, 0, 0, 0.00, NULL, GETUTCDATE(), GETUTCDATE()),

(@Loan2, @Customer2, @Agent2, DATEADD(day, -8, GETDATE()), 'Medium', 15000.00,
 DATEADD(day, 7, GETDATE()), 'Visited', DATEADD(day, -3, GETDATE()), 2, 1, 5000.00,
 DATEADD(day, -3, GETDATE()), GETUTCDATE(), GETUTCDATE()),

(@Loan3, @Customer3, @Agent3, DATEADD(day, -15, GETDATE()), 'Urgent', 45000.00,
 DATEADD(day, 2, GETDATE()), 'Visited', DATEADD(day, -1, GETDATE()), 3, 2, 15000.00,
 DATEADD(day, -5, GETDATE()), GETUTCDATE(), GETUTCDATE()),

(@Loan4, @Customer4, @Agent1, DATEADD(day, -5, GETDATE()), 'Medium', 8000.00,
 DATEADD(day, 10, GETDATE()), 'Assigned', NULL, 0, 0, 0.00, NULL, GETUTCDATE(), GETUTCDATE()),

(@Loan5, @Customer5, @Agent2, DATEADD(day, -2, GETDATE()), 'Low', 17708.00,
 DATEADD(day, 25, GETDATE()), 'Assigned', NULL, 0, 0, 0.00, NULL, GETUTCDATE(), GETUTCDATE());
GO

-- Insert Demo Visits
DECLARE @Lead1 UNIQUEIDENTIFIER = (SELECT Id FROM Leads WHERE LoanId = @Loan2);
DECLARE @Lead2 UNIQUEIDENTIFIER = (SELECT Id FROM Leads WHERE LoanId = @Loan3);

INSERT INTO Visits (LeadId, AgentId, VisitDate, Result, Notes, CheckInTime, CheckOutTime, DurationMinutes,
                   NextVisitScheduled, FollowUpNotes, CreatedAt)
VALUES 
(@Lead1, @Agent2, DATEADD(day, -5, GETDATE()), 'CustomerNotAvailable', 
 'Customer was not at home. Neighbor informed he will be back in evening.',
 DATEADD(day, -5, GETDATE()), DATEADD(minute, 15, DATEADD(day, -5, GETDATE())), 15,
 DATEADD(day, -3, GETDATE()), 'Visit in evening after 6 PM', GETUTCDATE()),

(@Lead1, @Agent2, DATEADD(day, -3, GETDATE()), 'PartialPayment',
 'Customer paid ₹5,000 and promised to pay remaining amount next week.',
 DATEADD(day, -3, GETDATE()), DATEADD(minute, 45, DATEADD(day, -3, GETDATE())), 45,
 DATEADD(day, 4, GETDATE()), 'Follow up for remaining ₹10,000', GETUTCDATE()),

(@Lead2, @Agent3, DATEADD(day, -10, GETDATE()), 'RefusedToPay',
 'Customer refused to pay citing financial difficulties.',
 DATEADD(day, -10, GETDATE()), DATEADD(minute, 30, DATEADD(day, -10, GETDATE())), 30,
 DATEADD(day, -5, GETDATE()), 'Discuss payment plan options', GETUTCDATE()),

(@Lead2, @Agent3, DATEADD(day, -5, GETDATE()), 'PartialPayment',
 'Negotiated payment plan. Customer paid ₹10,000.',
 DATEADD(day, -5, GETDATE()), DATEADD(minute, 60, DATEADD(day, -5, GETDATE())), 60,
 DATEADD(day, -1, GETDATE()), 'Collect next installment of ₹15,000', GETUTCDATE()),

(@Lead2, @Agent3, DATEADD(day, -1, GETDATE()), 'PartialPayment',
 'Customer paid ₹5,000. Promised to pay ₹10,000 more by weekend.',
 DATEADD(day, -1, GETDATE()), DATEADD(minute, 35, DATEADD(day, -1, GETDATE())), 35,
 DATEADD(day, 2, GETDATE()), 'Collect remaining ₹25,000', GETUTCDATE());
GO

-- Insert Demo Payments
DECLARE @Visit2 UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Visits WHERE Result = 'PartialPayment' AND AgentId = @Agent2);
DECLARE @Visit4 UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Visits WHERE Result = 'PartialPayment' AND AgentId = @Agent3 ORDER BY VisitDate);
DECLARE @Visit5 UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Visits WHERE Result = 'PartialPayment' AND AgentId = @Agent3 ORDER BY VisitDate DESC);

INSERT INTO Payments (LeadId, VisitId, AgentId, Amount, Method, Status, TransactionId, ReceiptNumber,
                     PaymentTimestamp, CreatedAt, UpdatedAt)
VALUES 
(@Lead1, @Visit2, @Agent2, 5000.00, 'Cash', 'Success', 'TXN001', 'RCP001',
 DATEADD(day, -3, GETDATE()), GETUTCDATE(), GETUTCDATE()),

(@Lead2, @Visit4, @Agent3, 10000.00, 'UPI', 'Success', 'UPI123456789', 'RCP002',
 DATEADD(day, -5, GETDATE()), GETUTCDATE(), GETUTCDATE()),

(@Lead2, @Visit5, @Agent3, 5000.00, 'Card', 'Success', 'CARD987654321', 'RCP003',
 DATEADD(day, -1, GETDATE()), GETUTCDATE(), GETUTCDATE());
GO

-- Insert Demo Notifications
INSERT INTO Notifications (AgentId, Title, Message, Type, Priority, SentAt, IsRead, CreatedAt)
VALUES 
(@Agent1, 'New Lead Assigned', 'You have been assigned a new high priority lead for LOAN001', 'Info', 'High', GETUTCDATE(), 0, GETUTCDATE()),
(@Agent1, 'Payment Overdue', 'Lead LOAN004 payment is overdue by 5 days', 'Warning', 'Medium', GETUTCDATE(), 0, GETUTCDATE()),
(@Agent2, 'Collection Target', 'You are 80% towards your monthly collection target', 'Success', 'Medium', GETUTCDATE(), 1, GETUTCDATE()),
(@Agent3, 'Urgent Follow-up', 'Lead LOAN003 requires urgent follow-up', 'Error', 'High', GETUTCDATE(), 0, GETUTCDATE()),
(NULL, 'System Maintenance', 'Scheduled maintenance on Sunday 2 AM - 4 AM', 'Info', 'Low', GETUTCDATE(), 0, GETUTCDATE());
GO

-- Insert Demo Agent Locations
INSERT INTO AgentLocations (AgentId, Location, Accuracy, Timestamp, CreatedAt)
VALUES 
(@Agent1, geography::Point(19.0760, 72.8777, 4326), 5.0, GETUTCDATE(), GETUTCDATE()), -- Mumbai
(@Agent2, geography::Point(18.5204, 73.8567, 4326), 8.0, GETUTCDATE(), GETUTCDATE()), -- Pune
(@Agent3, geography::Point(28.7041, 77.1025, 4326), 3.0, GETUTCDATE(), GETUTCDATE()); -- Delhi
GO

-- Insert Demo Documents
INSERT INTO Documents (LeadId, AgentId, DocumentType, FileName, FileUrl, FileSize, MimeType, UploadedAt)
VALUES 
(@Lead1, @Agent1, 'Photo', 'customer_house_photo.jpg', '/uploads/documents/customer_house_photo.jpg', 245760, 'image/jpeg', GETUTCDATE()),
(@Lead2, @Agent2, 'Receipt', 'payment_receipt_001.pdf', '/uploads/documents/payment_receipt_001.pdf', 102400, 'application/pdf', GETUTCDATE()),
(@Lead2, @Agent3, 'Signature', 'customer_signature.png', '/uploads/documents/customer_signature.png', 15360, 'image/png', GETUTCDATE());
GO

-- Insert Demo Sync Logs
INSERT INTO SyncLogs (AgentId, DeviceId, SyncType, Status, RecordsUploaded, RecordsDownloaded, 
                     StartedAt, CompletedAt, DurationSeconds, CreatedAt)
VALUES 
(@Agent1, 'DEVICE001', 'Upload', 'Success', 5, 0, DATEADD(minute, -30, GETUTCDATE()), DATEADD(minute, -28, GETUTCDATE()), 120, GETUTCDATE()),
(@Agent2, 'DEVICE002', 'Download', 'Success', 0, 12, DATEADD(hour, -2, GETUTCDATE()), DATEADD(hour, -2, GETUTCDATE()), 45, GETUTCDATE()),
(@Agent3, 'DEVICE003', 'FullSync', 'Partial', 8, 15, DATEADD(hour, -1, GETUTCDATE()), DATEADD(minute, -55, GETUTCDATE()), 300, GETUTCDATE());
GO

-- Update loan statistics based on payments
UPDATE Loans 
SET OutstandingAmount = OutstandingAmount - (
    SELECT ISNULL(SUM(p.Amount), 0) 
    FROM Payments p 
    INNER JOIN Leads l ON p.LeadId = l.Id 
    WHERE l.LoanId = Loans.Id AND p.Status = 'Success'
),
OverdueAmount = CASE 
    WHEN Id = @Loan1 THEN 20000.00  -- 25000 - 5000
    WHEN Id = @Loan3 THEN 30000.00  -- 45000 - 15000
    ELSE OverdueAmount 
END;
GO

-- Update lead statistics
UPDATE Leads 
SET TotalCollected = (
    SELECT ISNULL(SUM(p.Amount), 0) 
    FROM Payments p 
    WHERE p.LeadId = Leads.Id AND p.Status = 'Success'
);
GO

-- Update agent performance statistics
UPDATE Agents 
SET TotalCollections = (
    SELECT COUNT(*) 
    FROM Payments p 
    WHERE p.AgentId = Agents.Id AND p.Status = 'Success'
),
TotalAmountCollected = (
    SELECT ISNULL(SUM(p.Amount), 0) 
    FROM Payments p 
    WHERE p.AgentId = Agents.Id AND p.Status = 'Success'
);
GO

PRINT 'Demo data inserted successfully!';
PRINT 'Demo Agent Credentials:';
PRINT 'Phone: +91 9876543210, Password: demo123 (Rajesh Kumar)';
PRINT 'Phone: +91 9876543211, Password: demo123 (Priya Sharma)';
PRINT 'Phone: +91 9876543212, Password: demo123 (Amit Patel)';
PRINT 'Phone: +91 9876543213, Password: demo123 (Sunita Reddy - Manager)';
GO
