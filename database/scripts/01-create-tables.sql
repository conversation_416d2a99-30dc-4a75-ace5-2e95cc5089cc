-- FieldEZ Loan Collection System - SQL Server Database Schema
-- SQL Server 2019+ with spatial data support

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'FieldEZ')
BEGIN
    CREATE DATABASE FieldEZ;
END
GO

USE FieldEZ;
GO

-- Create custom data types
IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'AgentStatus')
BEGIN
    CREATE TYPE AgentStatus FROM NVARCHAR(20)
    CHECK (VALUE IN ('Active', 'Inactive', 'Suspended'));
END
GO

IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'LeadStatus')
BEGIN
    CREATE TYPE LeadStatus FROM NVARCHAR(20)
    CHECK (VALUE IN ('Assigned', 'Visited', 'Collected', 'Failed', 'Escalated'));
END
GO

IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'PaymentMethod')
BEGIN
    CREATE TYPE PaymentMethod FROM NVARCHAR(20)
    CHECK (VALUE IN ('Cash', 'UPI', 'Card', 'BankTransfer', 'Cheque'));
END
GO

-- Agents table
CREATE TABLE Agents (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId NVARCHAR(50) UNIQUE NOT NULL,
    Name NVARCHAR(255) NOT NULL,
    Phone NVARCHAR(20) UNIQUE NOT NULL,
    Email NVARCHAR(255) UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    Status AgentStatus DEFAULT 'Active',
    Role NVARCHAR(50) DEFAULT 'FieldAgent',
    
    -- Location data (using SQL Server spatial types)
    CurrentLocation GEOGRAPHY,
    TerritoryBounds GEOGRAPHY,
    BaseLocation GEOGRAPHY,
    
    -- Performance metrics
    TotalCollections INT DEFAULT 0,
    TotalAmountCollected DECIMAL(15,2) DEFAULT 0,
    SuccessRate DECIMAL(5,2) DEFAULT 0,
    AvgCollectionTimeMinutes INT DEFAULT 0,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    LastActiveAt DATETIME2,
    
    -- Constraints
    CONSTRAINT CK_Agents_Phone CHECK (Phone LIKE '+[0-9]%' AND LEN(Phone) >= 10),
    CONSTRAINT CK_Agents_Email CHECK (Email LIKE '%@%.%' OR Email IS NULL),
    CONSTRAINT CK_Agents_SuccessRate CHECK (SuccessRate >= 0 AND SuccessRate <= 100)
);
GO

-- Customers table
CREATE TABLE Customers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    Phone NVARCHAR(20) NOT NULL,
    Email NVARCHAR(255),
    
    -- Address information
    AddressLine1 NVARCHAR(255),
    AddressLine2 NVARCHAR(255),
    City NVARCHAR(100),
    State NVARCHAR(100),
    Pincode NVARCHAR(10),
    Country NVARCHAR(100) DEFAULT 'India',
    Coordinates GEOGRAPHY,
    
    -- Additional details
    DateOfBirth DATE,
    Occupation NVARCHAR(100),
    AnnualIncome DECIMAL(15,2),
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Customers_Phone CHECK (Phone LIKE '+[0-9]%' OR Phone LIKE '[0-9]%'),
    CONSTRAINT CK_Customers_AnnualIncome CHECK (AnnualIncome >= 0 OR AnnualIncome IS NULL)
);
GO

-- Loans table
CREATE TABLE Loans (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AccountNumber NVARCHAR(50) UNIQUE NOT NULL,
    CustomerId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Customers(Id),
    
    -- Loan details
    PrincipalAmount DECIMAL(15,2) NOT NULL,
    InterestRate DECIMAL(5,2) NOT NULL,
    TenureMonths INT NOT NULL,
    EMIAmount DECIMAL(15,2) NOT NULL,
    
    -- Current status
    OutstandingAmount DECIMAL(15,2) NOT NULL,
    OverdueAmount DECIMAL(15,2) DEFAULT 0,
    LastPaymentDate DATE,
    NextDueDate DATE NOT NULL,
    OverdueDays INT DEFAULT 0,
    
    -- Loan lifecycle
    DisbursedAt DATE NOT NULL,
    MaturityDate DATE NOT NULL,
    ClosedAt DATE,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Loans_Amounts CHECK (
        PrincipalAmount > 0 AND 
        OutstandingAmount >= 0 AND 
        OverdueAmount >= 0 AND
        EMIAmount > 0
    ),
    CONSTRAINT CK_Loans_InterestRate CHECK (InterestRate >= 0 AND InterestRate <= 100),
    CONSTRAINT CK_Loans_Tenure CHECK (TenureMonths > 0)
);
GO

-- Leads table (collection assignments)
CREATE TABLE Leads (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LoanId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Loans(Id),
    CustomerId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Customers(Id),
    AssignedAgentId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Agents(Id),
    
    -- Assignment details
    AssignedAt DATETIME2,
    Priority NVARCHAR(20) DEFAULT 'Medium' CHECK (Priority IN ('Low', 'Medium', 'High', 'Urgent')),
    ExpectedCollectionAmount DECIMAL(15,2),
    Deadline DATETIME2,
    
    -- Status tracking
    Status LeadStatus DEFAULT 'Assigned',
    LastVisitDate DATETIME2,
    VisitCount INT DEFAULT 0,
    SuccessfulVisits INT DEFAULT 0,
    
    -- Collection progress
    TotalCollected DECIMAL(15,2) DEFAULT 0,
    LastCollectionDate DATETIME2,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Leads_ExpectedAmount CHECK (ExpectedCollectionAmount > 0 OR ExpectedCollectionAmount IS NULL),
    CONSTRAINT CK_Leads_VisitCounts CHECK (SuccessfulVisits <= VisitCount)
);
GO

-- Visits table (agent visit records)
CREATE TABLE Visits (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LeadId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Leads(Id),
    AgentId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Agents(Id),
    
    -- Visit details
    VisitDate DATETIME2 NOT NULL,
    Location GEOGRAPHY,
    Result NVARCHAR(50) NOT NULL CHECK (Result IN ('PaymentCollected', 'PartialPayment', 'CustomerNotAvailable', 'RefusedToPay', 'Rescheduled')),
    Notes NVARCHAR(MAX),
    
    -- Duration tracking
    CheckInTime DATETIME2,
    CheckOutTime DATETIME2,
    DurationMinutes INT,
    
    -- Follow-up
    NextVisitScheduled DATETIME2,
    FollowUpNotes NVARCHAR(MAX),
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Visits_Duration CHECK (DurationMinutes >= 0 OR DurationMinutes IS NULL),
    CONSTRAINT CK_Visits_CheckOut CHECK (CheckOutTime >= CheckInTime OR CheckOutTime IS NULL)
);
GO

-- Payments table
CREATE TABLE Payments (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LeadId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Leads(Id),
    VisitId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Visits(Id),
    AgentId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Agents(Id),
    
    -- Payment details
    Amount DECIMAL(15,2) NOT NULL,
    Method PaymentMethod NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Success', 'Failed', 'Cancelled')),
    
    -- Transaction details
    TransactionId NVARCHAR(255),
    GatewayName NVARCHAR(100),
    GatewayResponse NVARCHAR(MAX), -- JSON response from gateway
    
    -- UPI specific
    UPIId NVARCHAR(255),
    UPIRefId NVARCHAR(255),
    
    -- Card specific
    CardLastFour NVARCHAR(4),
    CardType NVARCHAR(50),
    
    -- Receipt information
    ReceiptNumber NVARCHAR(100) UNIQUE,
    ReceiptUrl NVARCHAR(500),
    
    -- Location and timing
    PaymentLocation GEOGRAPHY,
    PaymentTimestamp DATETIME2 NOT NULL,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Payments_Amount CHECK (Amount > 0)
);
GO

-- Documents table (receipts, photos, signatures)
CREATE TABLE Documents (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    
    -- Relationships
    LeadId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Leads(Id),
    VisitId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Visits(Id),
    PaymentId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Payments(Id),
    AgentId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Agents(Id),
    
    -- Document details
    DocumentType NVARCHAR(50) NOT NULL CHECK (DocumentType IN ('Receipt', 'Photo', 'Signature', 'Agreement', 'Other')),
    FileName NVARCHAR(255) NOT NULL,
    FileUrl NVARCHAR(500) NOT NULL,
    FileSize BIGINT,
    MimeType NVARCHAR(100),
    
    -- Metadata
    UploadedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT CK_Documents_FileSize CHECK (FileSize > 0 OR FileSize IS NULL)
);
GO

-- Agent locations tracking
CREATE TABLE AgentLocations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AgentId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Agents(Id),
    Location GEOGRAPHY NOT NULL,
    Accuracy DECIMAL(8,2), -- GPS accuracy in meters
    Timestamp DATETIME2 NOT NULL,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);
GO

-- System notifications
CREATE TABLE Notifications (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AgentId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Agents(Id), -- NULL for broadcast notifications
    
    -- Notification content
    Title NVARCHAR(255) NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    Type NVARCHAR(50) DEFAULT 'Info' CHECK (Type IN ('Info', 'Warning', 'Error', 'Success')),
    Priority NVARCHAR(20) DEFAULT 'Medium' CHECK (Priority IN ('Low', 'Medium', 'High')),
    
    -- Delivery tracking
    SentAt DATETIME2 DEFAULT GETUTCDATE(),
    ReadAt DATETIME2,
    IsRead BIT DEFAULT 0,
    
    -- Action data
    ActionUrl NVARCHAR(500),
    ActionData NVARCHAR(MAX), -- JSON data
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);
GO

-- Sync logs for offline data synchronization
CREATE TABLE SyncLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AgentId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Agents(Id),
    DeviceId NVARCHAR(255) NOT NULL,
    
    -- Sync details
    SyncType NVARCHAR(50) NOT NULL CHECK (SyncType IN ('Upload', 'Download', 'FullSync')),
    Status NVARCHAR(50) NOT NULL CHECK (Status IN ('Pending', 'Success', 'Failed', 'Partial')),
    
    -- Data counts
    RecordsUploaded INT DEFAULT 0,
    RecordsDownloaded INT DEFAULT 0,
    ConflictsResolved INT DEFAULT 0,
    
    -- Timing
    StartedAt DATETIME2 NOT NULL,
    CompletedAt DATETIME2,
    DurationSeconds INT,
    
    -- Error handling
    ErrorMessage NVARCHAR(MAX),
    RetryCount INT DEFAULT 0,
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);
GO

-- Create triggers for UpdatedAt columns
CREATE TRIGGER TR_Agents_UpdatedAt ON Agents
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE Agents 
    SET UpdatedAt = GETUTCDATE() 
    WHERE Id IN (SELECT Id FROM inserted);
END
GO

CREATE TRIGGER TR_Customers_UpdatedAt ON Customers
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE Customers 
    SET UpdatedAt = GETUTCDATE() 
    WHERE Id IN (SELECT Id FROM inserted);
END
GO

CREATE TRIGGER TR_Loans_UpdatedAt ON Loans
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE Loans 
    SET UpdatedAt = GETUTCDATE() 
    WHERE Id IN (SELECT Id FROM inserted);
END
GO

CREATE TRIGGER TR_Leads_UpdatedAt ON Leads
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE Leads 
    SET UpdatedAt = GETUTCDATE() 
    WHERE Id IN (SELECT Id FROM inserted);
END
GO

CREATE TRIGGER TR_Payments_UpdatedAt ON Payments
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE Payments 
    SET UpdatedAt = GETUTCDATE() 
    WHERE Id IN (SELECT Id FROM inserted);
END
GO

PRINT 'Database tables created successfully!';
