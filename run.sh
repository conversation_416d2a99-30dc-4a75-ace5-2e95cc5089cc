#!/bin/bash

# FieldEZ Loan Collection System - Quick Start Script
# This script helps you quickly set up and run the application

set -e

echo "🚀 FieldEZ Loan Collection System - Quick Start"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check .NET
    if ! command -v dotnet &> /dev/null; then
        print_error ".NET 8 SDK is not installed. Please install it from https://dotnet.microsoft.com/download"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install it from https://nodejs.org"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js which includes npm"
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if SQL Server is accessible
    if command -v sqlcmd &> /dev/null; then
        print_status "Creating database and running scripts..."
        
        # Create database
        sqlcmd -S localhost -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'FieldEZ') CREATE DATABASE FieldEZ;" 2>/dev/null || {
            print_warning "Could not create database automatically. Please create 'FieldEZ' database manually in SQL Server"
        }
        
        # Run database scripts if they exist
        if [ -f "database/scripts/01-create-tables.sql" ]; then
            print_status "Running table creation script..."
            sqlcmd -S localhost -d FieldEZ -i database/scripts/01-create-tables.sql 2>/dev/null || {
                print_warning "Could not run table creation script automatically"
            }
        fi
        
        if [ -f "database/scripts/02-create-indexes.sql" ]; then
            print_status "Running indexes script..."
            sqlcmd -S localhost -d FieldEZ -i database/scripts/02-create-indexes.sql 2>/dev/null || {
                print_warning "Could not run indexes script automatically"
            }
        fi
        
        if [ -f "database/scripts/03-seed-data.sql" ]; then
            print_status "Running seed data script..."
            sqlcmd -S localhost -d FieldEZ -i database/scripts/03-seed-data.sql 2>/dev/null || {
                print_warning "Could not run seed data script automatically"
            }
        fi
        
        print_success "Database setup completed"
    else
        print_warning "sqlcmd not found. Please set up the database manually using SQL Server Management Studio"
        print_warning "1. Create database 'FieldEZ'"
        print_warning "2. Run scripts in database/scripts/ folder in order"
    fi
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    if [ -d "backend" ]; then
        cd backend
        
        print_status "Restoring NuGet packages..."
        dotnet restore
        
        print_status "Building backend..."
        dotnet build
        
        cd ..
        print_success "Backend setup completed"
    else
        print_error "Backend directory not found"
        exit 1
    fi
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    if [ -d "frontend" ]; then
        cd frontend
        
        print_status "Installing npm packages..."
        npm install
        
        # Create .env file if it doesn't exist
        if [ ! -f ".env" ]; then
            print_status "Creating .env file..."
            cat > .env << EOF
VITE_API_BASE_URL=http://localhost:5000/api
VITE_APP_NAME=FieldEZ Loan Collection
VITE_APP_VERSION=1.0.0
EOF
        fi
        
        cd ..
        print_success "Frontend setup completed"
    else
        print_error "Frontend directory not found"
        exit 1
    fi
}

# Start the application
start_application() {
    print_status "Starting FieldEZ Loan Collection System..."
    
    # Start backend in background
    print_status "Starting backend API..."
    cd backend/FieldEZ.API
    dotnet run &
    BACKEND_PID=$!
    cd ../..
    
    # Wait a moment for backend to start
    sleep 5
    
    # Start frontend
    print_status "Starting frontend..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    print_success "Application started successfully!"
    echo ""
    echo "🌐 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:5000"
    echo "📚 API Documentation: http://localhost:5000/swagger"
    echo ""
    echo "📱 Demo Login Credentials:"
    echo "   Phone: +91 9876543210"
    echo "   Password: demo123"
    echo ""
    echo "Press Ctrl+C to stop the application"
    
    # Wait for user to stop
    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
    wait
}

# Main execution
main() {
    case "${1:-setup}" in
        "setup")
            check_prerequisites
            setup_database
            setup_backend
            setup_frontend
            print_success "Setup completed! Run './run.sh start' to start the application"
            ;;
        "start")
            start_application
            ;;
        "backend")
            print_status "Starting backend only..."
            cd backend/FieldEZ.API
            dotnet run
            ;;
        "frontend")
            print_status "Starting frontend only..."
            cd frontend
            npm run dev
            ;;
        "help")
            echo "Usage: ./run.sh [command]"
            echo ""
            echo "Commands:"
            echo "  setup     - Set up the application (default)"
            echo "  start     - Start both backend and frontend"
            echo "  backend   - Start backend only"
            echo "  frontend  - Start frontend only"
            echo "  help      - Show this help message"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Run './run.sh help' for usage information"
            exit 1
            ;;
    esac
}

main "$@"
