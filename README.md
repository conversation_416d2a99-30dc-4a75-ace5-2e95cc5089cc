# FieldEZ Loan Collection System

A comprehensive loan collection management system built with React.js frontend, C# .NET backend, and SQL Server database.

## Technology Stack

### Frontend
- **React.js 18** with TypeScript
- **Material-UI (MUI)** for components
- **Redux Toolkit** for state management
- **React Query** for API calls
- **PWA** capabilities for offline support

### Backend
- **ASP.NET Core 8** Web API
- **Entity Framework Core** for ORM
- **SQL Server** database
- **JWT Authentication**
- **SignalR** for real-time updates

### Database
- **SQL Server 2019+**
- **Spatial data types** for location tracking
- **Optimized indexes** for performance

## Project Structure

```
FieldEZ-LoanCollection/
├── backend/
│   ├── FieldEZ.API/                 # Web API project
│   ├── FieldEZ.Core/                # Domain models and interfaces
│   ├── FieldEZ.Infrastructure/      # Data access and external services
│   ├── FieldEZ.Application/         # Business logic and services
│   └── FieldEZ.Tests/               # Unit and integration tests
├── frontend/
│   ├── public/                      # Static files and PWA manifest
│   ├── src/
│   │   ├── components/              # Reusable UI components
│   │   ├── pages/                   # Page components
│   │   ├── services/                # API services
│   │   ├── store/                   # Redux store and slices
│   │   ├── hooks/                   # Custom React hooks
│   │   ├── utils/                   # Utility functions
│   │   └── types/                   # TypeScript type definitions
│   ├── package.json
│   └── vite.config.ts
├── database/
│   ├── scripts/                     # SQL scripts for setup
│   └── migrations/                  # EF Core migrations
└── docs/
    ├── api/                         # API documentation
    └── deployment/                  # Deployment guides
```

## Features

### Core Functionality
- ✅ Agent authentication and management
- ✅ Intelligent lead distribution
- ✅ Multi-payment gateway integration
- ✅ Real-time location tracking
- ✅ Offline data synchronization
- ✅ Receipt generation and management
- ✅ Performance analytics and reporting
- ✅ LMS integration capabilities

### Agent Mobile Features
- 📱 Progressive Web App (PWA)
- 🔄 Offline-first architecture
- 📍 GPS location tracking
- 💳 Multiple payment methods (Cash, UPI, Cards)
- 📄 Digital receipt generation
- 📸 Document capture and upload
- 🔔 Real-time notifications
- 📊 Performance dashboard

### Admin Dashboard Features
- 👥 Agent management and monitoring
- 📋 Lead assignment and tracking
- 💰 Payment reconciliation
- 📈 Real-time analytics and reports
- ⚙️ System configuration
- 🔗 LMS integration management

## Quick Start

### Prerequisites
- .NET 8 SDK
- Node.js 18+
- SQL Server 2019+
- Visual Studio 2022 or VS Code

### Backend Setup
```bash
cd backend
dotnet restore
dotnet ef database update
dotnet run --project FieldEZ.API
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### Database Setup
```bash
# Run SQL scripts in order
sqlcmd -S localhost -d FieldEZ -i database/scripts/01-create-tables.sql
sqlcmd -S localhost -d FieldEZ -i database/scripts/02-create-indexes.sql
sqlcmd -S localhost -d FieldEZ -i database/scripts/03-seed-data.sql
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - Agent login
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - Logout

### Agents
- `GET /api/agents/profile` - Get agent profile
- `PUT /api/agents/profile` - Update agent profile
- `POST /api/agents/location` - Update location

### Leads
- `GET /api/leads` - Get assigned leads
- `GET /api/leads/{id}` - Get lead details
- `POST /api/leads/{id}/visit` - Record visit
- `PUT /api/leads/{id}/status` - Update lead status

### Payments
- `POST /api/payments/collect` - Process payment
- `GET /api/payments/history` - Payment history
- `GET /api/payments/{id}/receipt` - Download receipt

### Analytics
- `GET /api/analytics/dashboard` - Dashboard data
- `GET /api/analytics/performance` - Performance metrics
- `GET /api/analytics/reports` - Generate reports

## Development Guidelines

### Code Standards
- Follow C# coding conventions
- Use TypeScript for all React components
- Implement proper error handling
- Write unit tests for business logic
- Use async/await for asynchronous operations

### Security
- JWT token-based authentication
- Role-based authorization
- Input validation and sanitization
- HTTPS enforcement
- CORS configuration

### Performance
- Database query optimization
- Caching strategies (Redis)
- Lazy loading for React components
- Image optimization and compression
- API response compression

## Deployment

### Development
```bash
# Backend
cd backend
dotnet run --environment Development

# Frontend
cd frontend
npm run dev
```

### Production
```bash
# Backend
cd backend
dotnet publish -c Release -o ./publish
dotnet ./publish/FieldEZ.API.dll

# Frontend
cd frontend
npm run build
# Deploy build folder to web server
```

## Testing

### Backend Tests
```bash
cd backend
dotnet test
```

### Frontend Tests
```bash
cd frontend
npm run test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
