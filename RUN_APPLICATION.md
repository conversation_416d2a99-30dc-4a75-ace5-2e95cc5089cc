# FieldEZ Loan Collection System - Quick Start Guide

## Prerequisites

Before running the application, ensure you have the following installed:

- **.NET 8 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/8.0)
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **SQL Server 2019+** - [Download here](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
- **Git** - [Download here](https://git-scm.com/)

## Step 1: Setup Database

### 1.1 Create Database
Open SQL Server Management Studio (SSMS) or use sqlcmd:

```sql
-- Connect to SQL Server and create database
CREATE DATABASE FieldEZ;
GO
```

### 1.2 Run Database Scripts
```bash
# Navigate to database scripts directory
cd database/scripts

# Run the table creation script
sqlcmd -S localhost -d FieldEZ -i 01-create-tables.sql

# Run the indexes and views script
sqlcmd -S localhost -d FieldEZ -i 02-create-indexes.sql

# Optional: Run seed data script for demo
sqlcmd -S localhost -d FieldEZ -i 03-seed-data.sql
```

### 1.3 Verify Database Setup
```sql
-- Check if tables are created
USE FieldEZ;
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';
```

## Step 2: Backend Setup (.NET 8)

### 2.1 Navigate to Backend Directory
```bash
cd backend
```

### 2.2 Restore NuGet Packages
```bash
dotnet restore
```

### 2.3 Update Connection String
Edit `FieldEZ.API/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FieldEZ;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

### 2.4 Run Entity Framework Migrations (if needed)
```bash
cd FieldEZ.API
dotnet ef database update
```

### 2.5 Build the Backend
```bash
dotnet build
```

### 2.6 Run the Backend API
```bash
cd FieldEZ.API
dotnet run
```

The API will start on `https://localhost:5001` and `http://localhost:5000`

## Step 3: Frontend Setup (React.js)

### 3.1 Navigate to Frontend Directory
```bash
cd frontend
```

### 3.2 Install Dependencies
```bash
npm install
```

### 3.3 Create Environment File
Create `.env` file in the frontend directory:
```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_APP_NAME=FieldEZ Loan Collection
VITE_APP_VERSION=1.0.0
```

### 3.4 Run the Frontend
```bash
npm run dev
```

The frontend will start on `http://localhost:3000`

## Step 4: Access the Application

### 4.1 Open Browser
Navigate to: `http://localhost:3000`

### 4.2 Login with Demo Credentials
- **Phone**: +91 **********
- **Password**: demo123

## Step 5: Verify Everything is Working

### 5.1 Check API Health
```bash
curl http://localhost:5000/api/health
```

### 5.2 Test Authentication
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone": "+91**********", "password": "demo123"}'
```

### 5.3 Check Database Connection
```bash
curl http://localhost:5000/api/agents/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Issues
**Error**: Cannot connect to SQL Server
**Solution**:
- Ensure SQL Server is running
- Check connection string in appsettings.json
- Verify database exists
- Check Windows Authentication or SQL Server Authentication

#### 2. Port Already in Use
**Error**: Port 5000 or 3000 already in use
**Solution**:
```bash
# For backend (change port in launchSettings.json)
# For frontend
npm run dev -- --port 3001
```

#### 3. CORS Issues
**Error**: CORS policy error
**Solution**: Ensure CORS is configured in Program.cs:
```csharp
app.UseCors("AllowFrontend");
```

#### 4. Missing Dependencies
**Error**: Package not found
**Solution**:
```bash
# Backend
dotnet restore

# Frontend
npm install
```

#### 5. Database Schema Issues
**Error**: Table doesn't exist
**Solution**:
```bash
# Re-run database scripts
sqlcmd -S localhost -d FieldEZ -i database/scripts/01-create-tables.sql
```

## Development Mode

### Hot Reload
Both frontend and backend support hot reload:
- **Frontend**: Automatically reloads on file changes
- **Backend**: Use `dotnet watch run` for auto-restart

### Debug Mode
```bash
# Backend with debug
cd backend/FieldEZ.API
dotnet run --configuration Debug

# Frontend with debug
cd frontend
npm run dev
```

## Production Deployment

### Backend Production Build
```bash
cd backend/FieldEZ.API
dotnet publish -c Release -o ./publish
```

### Frontend Production Build
```bash
cd frontend
npm run build
```

## API Documentation

Once the backend is running, access Swagger documentation at:
`http://localhost:5000/swagger`

## Default Data

The application includes demo data:
- **Demo Agent**: Employee ID: EMP001, Phone: +91 **********
- **Demo Customer**: John Doe, Phone: +91 **********
- **Demo Loan**: Account: LOAN001, Amount: ₹1,00,000
- **Demo Lead**: High priority collection lead

## Next Steps

1. **Configure LMS Integration**: Update LMS plugin settings in appsettings.json
2. **Setup Payment Gateways**: Configure Razorpay/UPI settings
3. **Enable Notifications**: Setup SMS/Email services
4. **Configure Maps**: Add Google Maps API key
5. **Setup File Storage**: Configure document storage location

## Support

If you encounter any issues:
1. Check the console logs (both frontend and backend)
2. Review the troubleshooting section above
3. Ensure all prerequisites are installed
4. Verify database connectivity
5. Check firewall and antivirus settings

## Application Features

Once running, you can:
- ✅ Login as a field agent
- ✅ View assigned leads
- ✅ Record customer visits
- ✅ Process payments (Cash, UPI, Cards)
- ✅ Generate digital receipts
- ✅ Track location and routes
- ✅ View performance analytics
- ✅ Sync data with LMS systems
- ✅ Work offline and sync later

The application is now ready for use! 🚀
