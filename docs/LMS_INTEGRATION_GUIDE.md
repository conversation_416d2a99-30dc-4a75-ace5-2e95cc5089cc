# LMS Integration Guide - FieldEZ Loan Collection System

## Overview

FieldEZ provides a flexible plugin architecture to integrate with various Loan Management Systems (LMS). This guide covers how to configure, use, and develop custom LMS plugins.

## Supported LMS Systems

### 1. **Infosys Finacle**
- **Plugin**: FinaclePlugin
- **Type**: REST API Integration
- **Features**: Data sync, webhook support, reporting
- **Authentication**: API Key + Basic Auth

### 2. **Temenos T24**
- **Plugin**: TemenosPlugin  
- **Type**: REST API Integration
- **Features**: Data sync, real-time notifications, document management
- **Authentication**: OAuth 2.0

### 3. **Generic REST API**
- **Plugin**: GenericRestApiPlugin
- **Type**: Configurable REST API
- **Features**: Flexible endpoint mapping, multiple auth methods
- **Authentication**: Bear<PERSON>, Basic Auth, API Key

### 4. **Custom LMS**
- **Plugin**: Custom Plugin Development
- **Type**: Extensible plugin framework
- **Features**: Full customization support

## Plugin Architecture

### Core Interfaces

```csharp
// Base plugin interface
public interface ILMSPlugin
{
    string Name { get; }
    string Version { get; }
    LMSType Type { get; }
    bool IsEnabled { get; set; }
    
    Task<bool> TestConnectionAsync();
    Task InitializeAsync(Dictionary<string, string> configuration);
    Task<LMSHealthStatus> GetHealthStatusAsync();
}

// Data synchronization
public interface ILMSDataSync : ILMSPlugin
{
    Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null);
    Task<IEnumerable<LMSCustomer>> GetCustomersAsync(DateTime? lastSyncDate = null);
    Task<bool> UpdateLoanStatusAsync(string loanId, LoanStatus status);
    Task<bool> CreateCollectionRecordAsync(LMSCollectionRecord record);
    Task<SyncResult> SyncDataAsync(SyncRequest request);
}

// Webhook handling
public interface ILMSWebhookHandler : ILMSPlugin
{
    Task<WebhookResponse> HandleWebhookAsync(WebhookRequest request);
    Task<bool> RegisterWebhookAsync(string callbackUrl, IEnumerable<LMSEventType> eventTypes);
}

// Real-time notifications
public interface ILMSNotificationHandler : ILMSPlugin
{
    event EventHandler<LMSNotificationEventArgs> NotificationReceived;
    Task StartListeningAsync();
    Task StopListeningAsync();
}

// Document management
public interface ILMSDocumentManager : ILMSPlugin
{
    Task<string> UploadDocumentAsync(DocumentUploadRequest request);
    Task<Stream> DownloadDocumentAsync(string documentId);
    Task<IEnumerable<LMSDocument>> GetDocumentsAsync(string entityId, DocumentType type);
}
```

## Configuration

### 1. Finacle Configuration

```json
{
  "Name": "Finacle LMS Plugin",
  "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.FinaclePlugin, FieldEZ.Infrastructure",
  "Enabled": true,
  "RequireConnection": true,
  "Configuration": {
    "BaseUrl": "https://finacle-api.yourbank.com",
    "ApiKey": "your-finacle-api-key",
    "Username": "fieldez_user",
    "Password": "secure_password",
    "Timeout": "30000"
  }
}
```

### 2. Temenos Configuration

```json
{
  "Name": "Temenos T24 Plugin",
  "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.TemenosPlugin, FieldEZ.Infrastructure",
  "Enabled": true,
  "RequireConnection": true,
  "Configuration": {
    "BaseUrl": "https://t24-api.yourbank.com",
    "ClientId": "fieldez_client",
    "ClientSecret": "client_secret_here",
    "CallbackUrl": "https://fieldez.yourbank.com/api/lms/webhooks"
  }
}
```

### 3. Generic REST API Configuration

```json
{
  "Name": "Custom LMS Plugin",
  "TypeName": "FieldEZ.Infrastructure.LMS.Plugins.GenericRestApiPlugin, FieldEZ.Infrastructure",
  "Enabled": true,
  "RequireConnection": false,
  "Configuration": {
    "BaseUrl": "https://your-lms.com/api",
    "AuthType": "bearer",
    "BearerToken": "your-api-token",
    "DateFormat": "yyyy-MM-dd",
    "DateParameter": "since",
    
    // Endpoint mappings
    "Endpoint.Health": "/status",
    "Endpoint.GetLoans": "/loans",
    "Endpoint.GetCustomers": "/customers",
    "Endpoint.UpdateLoanStatus": "/loans/{loanId}/status",
    "Endpoint.CreateCollection": "/collections",
    
    // Data extraction paths
    "LoansDataPath": "result.loans",
    "CustomersDataPath": "result.customers",
    
    // Custom headers
    "Header.Accept": "application/json",
    "Header.X-Client-Version": "1.0"
  }
}
```

## API Usage

### 1. Plugin Management

```http
# Get all plugins
GET /api/lms/plugins

# Get plugin health status
GET /api/lms/plugins/health

# Enable/disable plugin
POST /api/lms/plugins/{pluginName}/enable
POST /api/lms/plugins/{pluginName}/disable
```

### 2. Data Synchronization

```http
# Sync all data
POST /api/lms/sync
Content-Type: application/json

{
  "lastSyncDate": "2024-01-01T00:00:00Z",
  "entityTypes": ["loans", "customers"],
  "batchSize": 100,
  "fullSync": false
}

# Get loans from LMS
GET /api/lms/loans?lastSyncDate=2024-01-01T00:00:00Z

# Get customers from LMS
GET /api/lms/customers?lastSyncDate=2024-01-01T00:00:00Z
```

### 3. Collection Records

```http
# Create collection record in LMS
POST /api/lms/collections
Content-Type: application/json

{
  "loanId": "LOAN123456",
  "agentId": "agent_001",
  "amount": 25000,
  "method": "UPI",
  "collectionDate": "2024-01-15T10:30:00Z",
  "transactionId": "txn_789",
  "receiptNumber": "RCP001",
  "notes": "Collected via UPI"
}

# Update loan status
PUT /api/lms/loans/{loanId}/status
Content-Type: application/json

{
  "status": "PartiallyPaid"
}
```

### 4. Webhook Handling

```http
# Handle incoming webhook
POST /api/lms/webhooks
Content-Type: application/json

{
  "webhookId": "webhook_123",
  "eventType": "PaymentReceived",
  "payload": "{\"loanId\":\"LOAN123\",\"amount\":5000}",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Data Models

### LMS Loan
```json
{
  "loanId": "LOAN123456",
  "accountNumber": "ACC789012",
  "customerId": "CUST001",
  "principalAmount": 100000,
  "outstandingAmount": 50000,
  "overdueAmount": 10000,
  "nextDueDate": "2024-02-01",
  "overdueDays": 15,
  "interestRate": 12.5,
  "emiAmount": 4500,
  "status": "Active",
  "createdDate": "2023-01-01T00:00:00Z",
  "lastUpdated": "2024-01-15T10:30:00Z",
  "customFields": {
    "branch": "Mumbai Central",
    "product": "Personal Loan"
  }
}
```

### LMS Customer
```json
{
  "customerId": "CUST001",
  "name": "John Doe",
  "phone": "+91-**********",
  "email": "<EMAIL>",
  "address": "123 Main Street",
  "city": "Mumbai",
  "state": "Maharashtra",
  "pincode": "400001",
  "createdDate": "2023-01-01T00:00:00Z",
  "lastUpdated": "2024-01-15T10:30:00Z",
  "customFields": {
    "segment": "Premium",
    "riskCategory": "Low"
  }
}
```

## Developing Custom Plugins

### 1. Create Plugin Class

```csharp
[AutoLoadPlugin(false)]
public class CustomLMSPlugin : ILMSDataSync, ILMSWebhookHandler
{
    public string Name => "Custom LMS Plugin";
    public string Version => "1.0.0";
    public string Description => "Integration with Custom LMS";
    public LMSType Type => LMSType.Custom;
    public bool IsEnabled { get; set; } = true;

    public async Task InitializeAsync(Dictionary<string, string> configuration)
    {
        // Initialize plugin with configuration
    }

    public async Task<bool> TestConnectionAsync()
    {
        // Test connection to LMS
        return true;
    }

    public async Task<IEnumerable<LMSLoan>> GetLoansAsync(DateTime? lastSyncDate = null)
    {
        // Implement loan retrieval logic
        return new List<LMSLoan>();
    }

    // Implement other interface methods...
}
```

### 2. Register Plugin

```csharp
// In Program.cs or Startup.cs
services.AddScoped<ILMSPlugin, CustomLMSPlugin>();
```

### 3. Configure Plugin

```json
{
  "Name": "Custom LMS Plugin",
  "TypeName": "YourNamespace.CustomLMSPlugin, YourAssembly",
  "Enabled": true,
  "Configuration": {
    "ApiUrl": "https://custom-lms.com/api",
    "ApiKey": "your-api-key"
  }
}
```

## Error Handling

### Common Error Scenarios

1. **Connection Failures**
   - Network timeouts
   - Authentication errors
   - Invalid endpoints

2. **Data Sync Issues**
   - Data format mismatches
   - Missing required fields
   - Duplicate records

3. **Webhook Processing**
   - Invalid payload format
   - Unrecognized event types
   - Processing failures

### Error Response Format

```json
{
  "success": false,
  "message": "Sync operation failed",
  "errors": [
    "Connection timeout to LMS",
    "Invalid loan status value"
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Monitoring and Logging

### Health Checks

```http
GET /api/lms/plugins/health

Response:
[
  {
    "pluginName": "Finacle LMS Plugin",
    "isHealthy": true,
    "status": "Healthy",
    "lastChecked": "2024-01-15T10:30:00Z",
    "responseTimeMs": 250,
    "details": {
      "statusCode": 200,
      "baseUrl": "https://finacle-api.example.com"
    }
  }
]
```

### Logging

```csharp
// Plugin logging
_logger.LogInformation("Plugin {PluginName} initialized successfully", Name);
_logger.LogError(ex, "Failed to sync data from {PluginName}", Name);
_logger.LogWarning("Plugin {PluginName} health check failed", Name);
```

## Security Considerations

### 1. Authentication
- Use secure authentication methods (OAuth 2.0, API keys)
- Store credentials securely (Azure Key Vault, environment variables)
- Implement token refresh mechanisms

### 2. Data Protection
- Encrypt sensitive data in transit and at rest
- Validate all input data
- Implement rate limiting

### 3. Access Control
- Use role-based access control
- Audit all LMS operations
- Monitor for suspicious activities

## Troubleshooting

### Common Issues

1. **Plugin Not Loading**
   - Check plugin configuration
   - Verify assembly references
   - Review application logs

2. **Sync Failures**
   - Validate API endpoints
   - Check authentication credentials
   - Review data format compatibility

3. **Webhook Issues**
   - Verify webhook registration
   - Check callback URL accessibility
   - Validate payload format

### Debug Mode

Enable debug logging in `appsettings.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "FieldEZ.Infrastructure.LMS": "Debug"
    }
  }
}
```

## Best Practices

1. **Configuration Management**
   - Use environment-specific configurations
   - Secure sensitive configuration data
   - Validate configuration on startup

2. **Error Handling**
   - Implement retry mechanisms with exponential backoff
   - Log detailed error information
   - Provide meaningful error messages

3. **Performance**
   - Implement caching for frequently accessed data
   - Use batch operations for bulk data sync
   - Monitor and optimize API response times

4. **Testing**
   - Write unit tests for plugin logic
   - Test with mock LMS responses
   - Perform integration testing with actual LMS

This guide provides a comprehensive overview of LMS integration capabilities in FieldEZ. For specific implementation details or custom requirements, refer to the plugin source code and API documentation.
