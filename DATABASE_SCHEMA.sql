-- Loan Collection System Database Schema
-- PostgreSQL 14+ with PostGIS extension for location data

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON><PERSON> custom types
CREATE TYPE agent_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE lead_status AS ENUM ('assigned', 'visited', 'collected', 'failed', 'escalated');
CREATE TYPE lead_priority AS ENUM ('low', 'medium', 'high', 'urgent');
CREATE TYPE payment_method AS ENUM ('cash', 'upi', 'card', 'bank_transfer', 'cheque');
CREATE TYPE payment_status AS ENUM ('pending', 'success', 'failed', 'cancelled');
CREATE TYPE visit_result AS ENUM ('payment_collected', 'partial_payment', 'customer_not_available', 'refused_to_pay', 'rescheduled');

-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id VARCHAR(50) UNIQUE NOT NULL,
    name VARC<PERSON>R(255) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    status agent_status DEFAULT 'active',
    role VARCHAR(50) DEFAULT 'field_agent',
    
    -- Location and territory
    current_location GEOGRAPHY(POINT, 4326),
    territory_bounds GEOGRAPHY(POLYGON, 4326),
    base_location GEOGRAPHY(POINT, 4326),
    
    -- Performance metrics
    total_collections INTEGER DEFAULT 0,
    total_amount_collected DECIMAL(15,2) DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    avg_collection_time INTEGER DEFAULT 0, -- in minutes
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT valid_phone CHECK (phone ~ '^\+?[1-9]\d{1,14}$'),
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Customers table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    
    -- Address information
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    pincode VARCHAR(10),
    country VARCHAR(100) DEFAULT 'India',
    coordinates GEOGRAPHY(POINT, 4326),
    
    -- Additional details
    date_of_birth DATE,
    occupation VARCHAR(100),
    annual_income DECIMAL(15,2),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_customer_phone CHECK (phone ~ '^\+?[1-9]\d{1,14}$')
);

-- Loans table
CREATE TABLE loans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID NOT NULL REFERENCES customers(id),
    
    -- Loan details
    principal_amount DECIMAL(15,2) NOT NULL,
    interest_rate DECIMAL(5,2) NOT NULL,
    tenure_months INTEGER NOT NULL,
    emi_amount DECIMAL(15,2) NOT NULL,
    
    -- Current status
    outstanding_amount DECIMAL(15,2) NOT NULL,
    overdue_amount DECIMAL(15,2) DEFAULT 0,
    last_payment_date DATE,
    next_due_date DATE NOT NULL,
    overdue_days INTEGER DEFAULT 0,
    
    -- Loan lifecycle
    disbursed_at DATE NOT NULL,
    maturity_date DATE NOT NULL,
    closed_at DATE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_amounts CHECK (
        principal_amount > 0 AND 
        outstanding_amount >= 0 AND 
        overdue_amount >= 0 AND
        emi_amount > 0
    )
);

-- Leads table (collection assignments)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    loan_id UUID NOT NULL REFERENCES loans(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    assigned_agent_id UUID REFERENCES agents(id),
    
    -- Assignment details
    assigned_at TIMESTAMP WITH TIME ZONE,
    priority lead_priority DEFAULT 'medium',
    expected_collection_amount DECIMAL(15,2),
    deadline TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    status lead_status DEFAULT 'assigned',
    last_visit_date TIMESTAMP WITH TIME ZONE,
    visit_count INTEGER DEFAULT 0,
    successful_visits INTEGER DEFAULT 0,
    
    -- Collection progress
    total_collected DECIMAL(15,2) DEFAULT 0,
    last_collection_date TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_expected_amount CHECK (expected_collection_amount > 0)
);

-- Visits table (agent visit records)
CREATE TABLE visits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    
    -- Visit details
    visit_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location GEOGRAPHY(POINT, 4326),
    result visit_result NOT NULL,
    notes TEXT,
    
    -- Duration tracking
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    
    -- Follow-up
    next_visit_scheduled TIMESTAMP WITH TIME ZONE,
    follow_up_notes TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_duration CHECK (duration_minutes >= 0),
    CONSTRAINT valid_checkout CHECK (check_out_time >= check_in_time)
);

-- Payments table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id),
    visit_id UUID REFERENCES visits(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    
    -- Payment details
    amount DECIMAL(15,2) NOT NULL,
    method payment_method NOT NULL,
    status payment_status DEFAULT 'pending',
    
    -- Transaction details
    transaction_id VARCHAR(255),
    gateway_name VARCHAR(100),
    gateway_response JSONB,
    
    -- UPI specific
    upi_id VARCHAR(255),
    upi_ref_id VARCHAR(255),
    
    -- Card specific
    card_last_four VARCHAR(4),
    card_type VARCHAR(50),
    
    -- Receipt information
    receipt_number VARCHAR(100) UNIQUE,
    receipt_url VARCHAR(500),
    
    -- Location and timing
    payment_location GEOGRAPHY(POINT, 4326),
    payment_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_payment_amount CHECK (amount > 0)
);

-- Documents table (receipts, photos, signatures)
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Relationships
    lead_id UUID REFERENCES leads(id),
    visit_id UUID REFERENCES visits(id),
    payment_id UUID REFERENCES payments(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    
    -- Document details
    document_type VARCHAR(50) NOT NULL, -- receipt, photo, signature, agreement
    file_name VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    
    -- Metadata
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_file_size CHECK (file_size > 0)
);

-- Agent locations tracking
CREATE TABLE agent_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES agents(id),
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    accuracy DECIMAL(8,2), -- GPS accuracy in meters
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES agents(id), -- NULL for broadcast notifications
    
    -- Notification content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- info, warning, error, success
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high
    
    -- Delivery tracking
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    is_read BOOLEAN DEFAULT FALSE,
    
    -- Action data
    action_url VARCHAR(500),
    action_data JSONB,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sync logs for offline data synchronization
CREATE TABLE sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES agents(id),
    device_id VARCHAR(255) NOT NULL,
    
    -- Sync details
    sync_type VARCHAR(50) NOT NULL, -- upload, download, full_sync
    status VARCHAR(50) NOT NULL, -- pending, success, failed, partial
    
    -- Data counts
    records_uploaded INTEGER DEFAULT 0,
    records_downloaded INTEGER DEFAULT 0,
    conflicts_resolved INTEGER DEFAULT 0,
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_location ON agents USING GIST(current_location);
CREATE INDEX idx_agents_phone ON agents(phone);

CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_location ON customers USING GIST(coordinates);

CREATE INDEX idx_loans_customer ON loans(customer_id);
CREATE INDEX idx_loans_account ON loans(account_number);
CREATE INDEX idx_loans_due_date ON loans(next_due_date);
CREATE INDEX idx_loans_overdue ON loans(overdue_days) WHERE overdue_days > 0;

CREATE INDEX idx_leads_agent ON leads(assigned_agent_id);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_priority ON leads(priority);
CREATE INDEX idx_leads_deadline ON leads(deadline);
CREATE INDEX idx_leads_customer ON leads(customer_id);

CREATE INDEX idx_visits_lead ON visits(lead_id);
CREATE INDEX idx_visits_agent ON visits(agent_id);
CREATE INDEX idx_visits_date ON visits(visit_date);
CREATE INDEX idx_visits_location ON visits USING GIST(location);

CREATE INDEX idx_payments_lead ON payments(lead_id);
CREATE INDEX idx_payments_agent ON payments(agent_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_timestamp ON payments(payment_timestamp);
CREATE INDEX idx_payments_method ON payments(method);

CREATE INDEX idx_documents_lead ON documents(lead_id);
CREATE INDEX idx_documents_type ON documents(document_type);

CREATE INDEX idx_agent_locations_agent ON agent_locations(agent_id);
CREATE INDEX idx_agent_locations_timestamp ON agent_locations(timestamp);
CREATE INDEX idx_agent_locations_location ON agent_locations USING GIST(location);

CREATE INDEX idx_notifications_agent ON notifications(agent_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);

CREATE INDEX idx_sync_logs_agent ON sync_logs(agent_id);
CREATE INDEX idx_sync_logs_status ON sync_logs(status);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_loans_updated_at BEFORE UPDATE ON loans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for common queries
CREATE VIEW agent_performance AS
SELECT 
    a.id,
    a.name,
    a.phone,
    a.status,
    COUNT(DISTINCT l.id) as total_leads,
    COUNT(DISTINCT v.id) as total_visits,
    COUNT(DISTINCT p.id) as total_payments,
    COALESCE(SUM(p.amount), 0) as total_collected,
    ROUND(
        CASE 
            WHEN COUNT(DISTINCT l.id) > 0 
            THEN (COUNT(DISTINCT CASE WHEN l.status = 'collected' THEN l.id END) * 100.0 / COUNT(DISTINCT l.id))
            ELSE 0 
        END, 2
    ) as success_rate
FROM agents a
LEFT JOIN leads l ON a.id = l.assigned_agent_id
LEFT JOIN visits v ON a.id = v.agent_id
LEFT JOIN payments p ON a.id = p.agent_id AND p.status = 'success'
GROUP BY a.id, a.name, a.phone, a.status;

CREATE VIEW overdue_leads AS
SELECT 
    l.id as lead_id,
    l.priority,
    l.assigned_agent_id,
    c.name as customer_name,
    c.phone as customer_phone,
    ln.account_number,
    ln.overdue_amount,
    ln.overdue_days,
    l.expected_collection_amount,
    l.deadline,
    ST_AsText(c.coordinates) as customer_location
FROM leads l
JOIN customers c ON l.customer_id = c.id
JOIN loans ln ON l.loan_id = ln.id
WHERE l.status IN ('assigned', 'visited') 
AND ln.overdue_days > 0
ORDER BY l.priority DESC, ln.overdue_days DESC;
