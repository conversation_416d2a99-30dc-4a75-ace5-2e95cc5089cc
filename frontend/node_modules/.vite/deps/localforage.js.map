{"version": 3, "sources": ["../../localforage/dist/localforage.js"], "sourcesContent": ["/*!\n    localForage -- Offline Storage, Improved\n    Version 1.10.0\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.localforage = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw (f.code=\"MODULE_NOT_FOUND\", f)}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nvar Mutation = global.MutationObserver || global.WebKitMutationObserver;\n\nvar scheduleDrain;\n\n{\n  if (Mutation) {\n    var called = 0;\n    var observer = new Mutation(nextTick);\n    var element = global.document.createTextNode('');\n    observer.observe(element, {\n      characterData: true\n    });\n    scheduleDrain = function () {\n      element.data = (called = ++called % 2);\n    };\n  } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n    var channel = new global.MessageChannel();\n    channel.port1.onmessage = nextTick;\n    scheduleDrain = function () {\n      channel.port2.postMessage(0);\n    };\n  } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n    scheduleDrain = function () {\n\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var scriptEl = global.document.createElement('script');\n      scriptEl.onreadystatechange = function () {\n        nextTick();\n\n        scriptEl.onreadystatechange = null;\n        scriptEl.parentNode.removeChild(scriptEl);\n        scriptEl = null;\n      };\n      global.document.documentElement.appendChild(scriptEl);\n    };\n  } else {\n    scheduleDrain = function () {\n      setTimeout(nextTick, 0);\n    };\n  }\n}\n\nvar draining;\nvar queue = [];\n//named nextTick for less confusing stack traces\nfunction nextTick() {\n  draining = true;\n  var i, oldQueue;\n  var len = queue.length;\n  while (len) {\n    oldQueue = queue;\n    queue = [];\n    i = -1;\n    while (++i < len) {\n      oldQueue[i]();\n    }\n    len = queue.length;\n  }\n  draining = false;\n}\n\nmodule.exports = immediate;\nfunction immediate(task) {\n  if (queue.push(task) === 1 && !draining) {\n    scheduleDrain();\n  }\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{}],2:[function(_dereq_,module,exports){\n'use strict';\nvar immediate = _dereq_(1);\n\n/* istanbul ignore next */\nfunction INTERNAL() {}\n\nvar handlers = {};\n\nvar REJECTED = ['REJECTED'];\nvar FULFILLED = ['FULFILLED'];\nvar PENDING = ['PENDING'];\n\nmodule.exports = Promise;\n\nfunction Promise(resolver) {\n  if (typeof resolver !== 'function') {\n    throw new TypeError('resolver must be a function');\n  }\n  this.state = PENDING;\n  this.queue = [];\n  this.outcome = void 0;\n  if (resolver !== INTERNAL) {\n    safelyResolveThenable(this, resolver);\n  }\n}\n\nPromise.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\nPromise.prototype.then = function (onFulfilled, onRejected) {\n  if (typeof onFulfilled !== 'function' && this.state === FULFILLED ||\n    typeof onRejected !== 'function' && this.state === REJECTED) {\n    return this;\n  }\n  var promise = new this.constructor(INTERNAL);\n  if (this.state !== PENDING) {\n    var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n    unwrap(promise, resolver, this.outcome);\n  } else {\n    this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n  }\n\n  return promise;\n};\nfunction QueueItem(promise, onFulfilled, onRejected) {\n  this.promise = promise;\n  if (typeof onFulfilled === 'function') {\n    this.onFulfilled = onFulfilled;\n    this.callFulfilled = this.otherCallFulfilled;\n  }\n  if (typeof onRejected === 'function') {\n    this.onRejected = onRejected;\n    this.callRejected = this.otherCallRejected;\n  }\n}\nQueueItem.prototype.callFulfilled = function (value) {\n  handlers.resolve(this.promise, value);\n};\nQueueItem.prototype.otherCallFulfilled = function (value) {\n  unwrap(this.promise, this.onFulfilled, value);\n};\nQueueItem.prototype.callRejected = function (value) {\n  handlers.reject(this.promise, value);\n};\nQueueItem.prototype.otherCallRejected = function (value) {\n  unwrap(this.promise, this.onRejected, value);\n};\n\nfunction unwrap(promise, func, value) {\n  immediate(function () {\n    var returnValue;\n    try {\n      returnValue = func(value);\n    } catch (e) {\n      return handlers.reject(promise, e);\n    }\n    if (returnValue === promise) {\n      handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n    } else {\n      handlers.resolve(promise, returnValue);\n    }\n  });\n}\n\nhandlers.resolve = function (self, value) {\n  var result = tryCatch(getThen, value);\n  if (result.status === 'error') {\n    return handlers.reject(self, result.value);\n  }\n  var thenable = result.value;\n\n  if (thenable) {\n    safelyResolveThenable(self, thenable);\n  } else {\n    self.state = FULFILLED;\n    self.outcome = value;\n    var i = -1;\n    var len = self.queue.length;\n    while (++i < len) {\n      self.queue[i].callFulfilled(value);\n    }\n  }\n  return self;\n};\nhandlers.reject = function (self, error) {\n  self.state = REJECTED;\n  self.outcome = error;\n  var i = -1;\n  var len = self.queue.length;\n  while (++i < len) {\n    self.queue[i].callRejected(error);\n  }\n  return self;\n};\n\nfunction getThen(obj) {\n  // Make sure we only access the accessor once as required by the spec\n  var then = obj && obj.then;\n  if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n    return function appyThen() {\n      then.apply(obj, arguments);\n    };\n  }\n}\n\nfunction safelyResolveThenable(self, thenable) {\n  // Either fulfill, reject or reject with error\n  var called = false;\n  function onError(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.reject(self, value);\n  }\n\n  function onSuccess(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.resolve(self, value);\n  }\n\n  function tryToUnwrap() {\n    thenable(onSuccess, onError);\n  }\n\n  var result = tryCatch(tryToUnwrap);\n  if (result.status === 'error') {\n    onError(result.value);\n  }\n}\n\nfunction tryCatch(func, value) {\n  var out = {};\n  try {\n    out.value = func(value);\n    out.status = 'success';\n  } catch (e) {\n    out.status = 'error';\n    out.value = e;\n  }\n  return out;\n}\n\nPromise.resolve = resolve;\nfunction resolve(value) {\n  if (value instanceof this) {\n    return value;\n  }\n  return handlers.resolve(new this(INTERNAL), value);\n}\n\nPromise.reject = reject;\nfunction reject(reason) {\n  var promise = new this(INTERNAL);\n  return handlers.reject(promise, reason);\n}\n\nPromise.all = all;\nfunction all(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var values = new Array(len);\n  var resolved = 0;\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    allResolver(iterable[i], i);\n  }\n  return promise;\n  function allResolver(value, i) {\n    self.resolve(value).then(resolveFromAll, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n    function resolveFromAll(outValue) {\n      values[i] = outValue;\n      if (++resolved === len && !called) {\n        called = true;\n        handlers.resolve(promise, values);\n      }\n    }\n  }\n}\n\nPromise.race = race;\nfunction race(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    resolver(iterable[i]);\n  }\n  return promise;\n  function resolver(value) {\n    self.resolve(value).then(function (response) {\n      if (!called) {\n        called = true;\n        handlers.resolve(promise, response);\n      }\n    }, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n  }\n}\n\n},{\"1\":1}],3:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nif (typeof global.Promise !== 'function') {\n  global.Promise = _dereq_(2);\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{\"2\":2}],4:[function(_dereq_,module,exports){\n'use strict';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction getIDB() {\n    /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n    try {\n        if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n        }\n        if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n        }\n        if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n        }\n        if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n        }\n        if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n        }\n    } catch (e) {\n        return;\n    }\n}\n\nvar idb = getIDB();\n\nfunction isIndexedDBValid() {\n    try {\n        // Initialize IndexedDB; fall back to vendor-prefixed versions\n        // if needed.\n        if (!idb || !idb.open) {\n            return false;\n        }\n        // We mimic PouchDB here;\n        //\n        // We test for openDatabase because IE Mobile identifies itself\n        // as Safari. Oh the lulz...\n        var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n\n        var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n        // Safari <10.1 does not meet our requirements for IDB support\n        // (see: https://github.com/pouchdb/pouchdb/issues/5572).\n        // Safari 10.1 shipped with fetch, we can use that to detect it.\n        // Note: this creates issues with `window.fetch` polyfills and\n        // overrides; see:\n        // https://github.com/localForage/localForage/issues/856\n        return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n        // some outdated implementations of IDB that appear on Samsung\n        // and HTC Android devices <4.4 are missing IDBKeyRange\n        // See: https://github.com/mozilla/localForage/issues/128\n        // See: https://github.com/mozilla/localForage/issues/272\n        typeof IDBKeyRange !== 'undefined';\n    } catch (e) {\n        return false;\n    }\n}\n\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\nfunction createBlob(parts, properties) {\n    /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n    parts = parts || [];\n    properties = properties || {};\n    try {\n        return new Blob(parts, properties);\n    } catch (e) {\n        if (e.name !== 'TypeError') {\n            throw e;\n        }\n        var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n        var builder = new Builder();\n        for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n        }\n        return builder.getBlob(properties.type);\n    }\n}\n\n// This is CommonJS because lie is an external dependency, so Rollup\n// can just ignore it.\nif (typeof Promise === 'undefined') {\n    // In the \"nopromises\" build this will just throw if you don't have\n    // a global promise object, but it would throw anyway later.\n    _dereq_(3);\n}\nvar Promise$1 = Promise;\n\nfunction executeCallback(promise, callback) {\n    if (callback) {\n        promise.then(function (result) {\n            callback(null, result);\n        }, function (error) {\n            callback(error);\n        });\n    }\n}\n\nfunction executeTwoCallbacks(promise, callback, errorCallback) {\n    if (typeof callback === 'function') {\n        promise.then(callback);\n    }\n\n    if (typeof errorCallback === 'function') {\n        promise[\"catch\"](errorCallback);\n    }\n}\n\nfunction normalizeKey(key) {\n    // Cast the key to a string, as that's all we can set as a key.\n    if (typeof key !== 'string') {\n        console.warn(key + ' used as a key, but it is not a string.');\n        key = String(key);\n    }\n\n    return key;\n}\n\nfunction getCallback() {\n    if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n        return arguments[arguments.length - 1];\n    }\n}\n\n// Some code originally from async_storage.js in\n// [Gaia](https://github.com/mozilla-b2g/gaia).\n\nvar DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\nvar supportsBlobs = void 0;\nvar dbContexts = {};\nvar toString = Object.prototype.toString;\n\n// Transaction Modes\nvar READ_ONLY = 'readonly';\nvar READ_WRITE = 'readwrite';\n\n// Transform a binary string to an array buffer, because otherwise\n// weird stuff happens when you try to work with the binary string directly.\n// It is known.\n// From http://stackoverflow.com/questions/14967647/ (continues on next line)\n// encode-decode-image-with-base64-breaks-image (2013-04-21)\nfunction _binStringToArrayBuffer(bin) {\n    var length = bin.length;\n    var buf = new ArrayBuffer(length);\n    var arr = new Uint8Array(buf);\n    for (var i = 0; i < length; i++) {\n        arr[i] = bin.charCodeAt(i);\n    }\n    return buf;\n}\n\n//\n// Blobs are not supported in all versions of IndexedDB, notably\n// Chrome <37 and Android <5. In those versions, storing a blob will throw.\n//\n// Various other blob bugs exist in Chrome v37-42 (inclusive).\n// Detecting them is expensive and confusing to users, and Chrome 37-42\n// is at very low usage worldwide, so we do a hacky userAgent check instead.\n//\n// content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n// 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n// FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n//\n// Code borrowed from PouchDB. See:\n// https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n//\nfunction _checkBlobSupportWithoutCaching(idb) {\n    return new Promise$1(function (resolve) {\n        var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n        var blob = createBlob(['']);\n        txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n\n        txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n        };\n\n        txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n        };\n    })[\"catch\"](function () {\n        return false; // error, so assume unsupported\n    });\n}\n\nfunction _checkBlobSupport(idb) {\n    if (typeof supportsBlobs === 'boolean') {\n        return Promise$1.resolve(supportsBlobs);\n    }\n    return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n        supportsBlobs = value;\n        return supportsBlobs;\n    });\n}\n\nfunction _deferReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Create a deferred object representing the current database operation.\n    var deferredOperation = {};\n\n    deferredOperation.promise = new Promise$1(function (resolve, reject) {\n        deferredOperation.resolve = resolve;\n        deferredOperation.reject = reject;\n    });\n\n    // Enqueue the deferred operation.\n    dbContext.deferredOperations.push(deferredOperation);\n\n    // Chain its promise to the database readiness.\n    if (!dbContext.dbReady) {\n        dbContext.dbReady = deferredOperation.promise;\n    } else {\n        dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n        });\n    }\n}\n\nfunction _advanceReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Resolve its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.resolve();\n        return deferredOperation.promise;\n    }\n}\n\nfunction _rejectReadiness(dbInfo, err) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Reject its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.reject(err);\n        return deferredOperation.promise;\n    }\n}\n\nfunction _getConnection(dbInfo, upgradeNeeded) {\n    return new Promise$1(function (resolve, reject) {\n        dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n\n        if (dbInfo.db) {\n            if (upgradeNeeded) {\n                _deferReadiness(dbInfo);\n                dbInfo.db.close();\n            } else {\n                return resolve(dbInfo.db);\n            }\n        }\n\n        var dbArgs = [dbInfo.name];\n\n        if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n        }\n\n        var openreq = idb.open.apply(idb, dbArgs);\n\n        if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n                var db = openreq.result;\n                try {\n                    db.createObjectStore(dbInfo.storeName);\n                    if (e.oldVersion <= 1) {\n                        // Added when support for blob shims was added\n                        db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                    }\n                } catch (ex) {\n                    if (ex.name === 'ConstraintError') {\n                        console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                    } else {\n                        throw ex;\n                    }\n                }\n            };\n        }\n\n        openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n        };\n\n        openreq.onsuccess = function () {\n            var db = openreq.result;\n            db.onversionchange = function (e) {\n                // Triggered when the database is modified (e.g. adding an objectStore) or\n                // deleted (even when initiated by other sessions in different tabs).\n                // Closing the connection here prevents those operations from being blocked.\n                // If the database is accessed again later by this instance, the connection\n                // will be reopened or the database recreated as needed.\n                e.target.close();\n            };\n            resolve(db);\n            _advanceReadiness(dbInfo);\n        };\n    });\n}\n\nfunction _getOriginalConnection(dbInfo) {\n    return _getConnection(dbInfo, false);\n}\n\nfunction _getUpgradedConnection(dbInfo) {\n    return _getConnection(dbInfo, true);\n}\n\nfunction _isUpgradeNeeded(dbInfo, defaultVersion) {\n    if (!dbInfo.db) {\n        return true;\n    }\n\n    var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n    var isDowngrade = dbInfo.version < dbInfo.db.version;\n    var isUpgrade = dbInfo.version > dbInfo.db.version;\n\n    if (isDowngrade) {\n        // If the version is not the default one\n        // then warn for impossible downgrade.\n        if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n        }\n        // Align the versions to prevent errors.\n        dbInfo.version = dbInfo.db.version;\n    }\n\n    if (isUpgrade || isNewStore) {\n        // If the store is new then increment the version (if needed).\n        // This will trigger an \"upgradeneeded\" event which is required\n        // for creating a store.\n        if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n                dbInfo.version = incVersion;\n            }\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n// encode a blob for indexeddb engines that don't support blobs\nfunction _encodeBlob(blob) {\n    return new Promise$1(function (resolve, reject) {\n        var reader = new FileReader();\n        reader.onerror = reject;\n        reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n                __local_forage_encoded_blob: true,\n                data: base64,\n                type: blob.type\n            });\n        };\n        reader.readAsBinaryString(blob);\n    });\n}\n\n// decode an encoded blob\nfunction _decodeBlob(encodedBlob) {\n    var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n    return createBlob([arrayBuff], { type: encodedBlob.type });\n}\n\n// is this one of our fancy encoded blobs?\nfunction _isEncodedBlob(value) {\n    return value && value.__local_forage_encoded_blob;\n}\n\n// Specialize the default `ready()` function by making it dependent\n// on the current database operations. Thus, the driver will be actually\n// ready when it's been initialized (default) *and* there are no pending\n// operations on the database (initiated by some other instances).\nfunction _fullyReady(callback) {\n    var self = this;\n\n    var promise = self._initReady().then(function () {\n        var dbContext = dbContexts[self._dbInfo.name];\n\n        if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n        }\n    });\n\n    executeTwoCallbacks(promise, callback, callback);\n    return promise;\n}\n\n// Try to establish a new db connection to replace the\n// current one which is broken (i.e. experiencing\n// InvalidStateError while creating a transaction).\nfunction _tryReconnect(dbInfo) {\n    _deferReadiness(dbInfo);\n\n    var dbContext = dbContexts[dbInfo.name];\n    var forages = dbContext.forages;\n\n    for (var i = 0; i < forages.length; i++) {\n        var forage = forages[i];\n        if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n        }\n    }\n    dbInfo.db = null;\n\n    return _getOriginalConnection(dbInfo).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        // store the latest db reference\n        // in case the db was upgraded\n        dbInfo.db = dbContext.db = db;\n        for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n        }\n    })[\"catch\"](function (err) {\n        _rejectReadiness(dbInfo, err);\n        throw err;\n    });\n}\n\n// FF doesn't like Promises (micro-tasks) and IDDB store operations,\n// so we have to do it with callbacks\nfunction createTransaction(dbInfo, mode, callback, retries) {\n    if (retries === undefined) {\n        retries = 1;\n    }\n\n    try {\n        var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n        callback(null, tx);\n    } catch (err) {\n        if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n                if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                    // increase the db version, to create the new ObjectStore\n                    if (dbInfo.db) {\n                        dbInfo.version = dbInfo.db.version + 1;\n                    }\n                    // Reopen the database for upgrading.\n                    return _getUpgradedConnection(dbInfo);\n                }\n            }).then(function () {\n                return _tryReconnect(dbInfo).then(function () {\n                    createTransaction(dbInfo, mode, callback, retries - 1);\n                });\n            })[\"catch\"](callback);\n        }\n\n        callback(err);\n    }\n}\n\nfunction createDbContext() {\n    return {\n        // Running localForages sharing a database.\n        forages: [],\n        // Shared database.\n        db: null,\n        // Database readiness (promise).\n        dbReady: null,\n        // Deferred operations on the database.\n        deferredOperations: []\n    };\n}\n\n// Open the IndexedDB database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    // Get the current context of the database;\n    var dbContext = dbContexts[dbInfo.name];\n\n    // ...or create a new context.\n    if (!dbContext) {\n        dbContext = createDbContext();\n        // Register the new context in the global container.\n        dbContexts[dbInfo.name] = dbContext;\n    }\n\n    // Register itself as a running localForage in the current context.\n    dbContext.forages.push(self);\n\n    // Replace the default `ready()` function with the specialized one.\n    if (!self._initReady) {\n        self._initReady = self.ready;\n        self.ready = _fullyReady;\n    }\n\n    // Create an array of initialization states of the related localForages.\n    var initPromises = [];\n\n    function ignoreErrors() {\n        // Don't handle errors here,\n        // just makes sure related localForages aren't pending.\n        return Promise$1.resolve();\n    }\n\n    for (var j = 0; j < dbContext.forages.length; j++) {\n        var forage = dbContext.forages[j];\n        if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n        }\n    }\n\n    // Take a snapshot of the related localForages.\n    var forages = dbContext.forages.slice(0);\n\n    // Initialize the connection process only when\n    // all the related localForages aren't pending.\n    return Promise$1.all(initPromises).then(function () {\n        dbInfo.db = dbContext.db;\n        // Get the connection or open a new one without upgrade.\n        return _getOriginalConnection(dbInfo);\n    }).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        dbInfo.db = dbContext.db = db;\n        self._dbInfo = dbInfo;\n        // Share the final connection amongst related localForages.\n        for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n                // Self is already up-to-date.\n                forage._dbInfo.db = dbInfo.db;\n                forage._dbInfo.version = dbInfo.version;\n            }\n        }\n    });\n}\n\nfunction getItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.get(key);\n\n                    req.onsuccess = function () {\n                        var value = req.result;\n                        if (value === undefined) {\n                            value = null;\n                        }\n                        if (_isEncodedBlob(value)) {\n                            value = _decodeBlob(value);\n                        }\n                        resolve(value);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items stored in database.\nfunction iterate(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openCursor();\n                    var iterationNumber = 1;\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (cursor) {\n                            var value = cursor.value;\n                            if (_isEncodedBlob(value)) {\n                                value = _decodeBlob(value);\n                            }\n                            var result = iterator(value, cursor.key, iterationNumber++);\n\n                            // when the iterator callback returns any\n                            // (non-`undefined`) value, then we stop\n                            // the iteration immediately\n                            if (result !== void 0) {\n                                resolve(result);\n                            } else {\n                                cursor[\"continue\"]();\n                            }\n                        } else {\n                            resolve();\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n\n    return promise;\n}\n\nfunction setItem(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        var dbInfo;\n        self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n                return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                    if (blobSupport) {\n                        return value;\n                    }\n                    return _encodeBlob(value);\n                });\n            }\n            return value;\n        }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n\n                    // The reason we don't _save_ null is because IE 10 does\n                    // not support saving the `null` type in IndexedDB. How\n                    // ironic, given the bug below!\n                    // See: https://github.com/mozilla/localForage/issues/161\n                    if (value === null) {\n                        value = undefined;\n                    }\n\n                    var req = store.put(value, key);\n\n                    transaction.oncomplete = function () {\n                        // Cast to undefined so the value passed to\n                        // callback/promise is the same as what one would get out\n                        // of `getItem()` later. This leads to some weirdness\n                        // (setItem('foo', undefined) will return `null`), but\n                        // it's not my fault localStorage is our baseline and that\n                        // it's weird.\n                        if (value === undefined) {\n                            value = null;\n                        }\n\n                        resolve(value);\n                    };\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction removeItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    // We use a Grunt task to make this safe for IE and some\n                    // versions of Android (including those used by Cordova).\n                    // Normally IE won't like `.delete()` and will insist on\n                    // using `['delete']()`, but we have a build step that\n                    // fixes this for us now.\n                    var req = store[\"delete\"](key);\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onerror = function () {\n                        reject(req.error);\n                    };\n\n                    // The request will be also be aborted if we've exceeded our storage\n                    // space.\n                    transaction.onabort = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction clear(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.clear();\n\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction length(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.count();\n\n                    req.onsuccess = function () {\n                        resolve(req.result);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction key(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        if (n < 0) {\n            resolve(null);\n\n            return;\n        }\n\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var advanced = false;\n                    var req = store.openKeyCursor();\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n                        if (!cursor) {\n                            // this means there weren't enough keys\n                            resolve(null);\n\n                            return;\n                        }\n\n                        if (n === 0) {\n                            // We have the first key, return it if that's what they\n                            // wanted.\n                            resolve(cursor.key);\n                        } else {\n                            if (!advanced) {\n                                // Otherwise, ask the cursor to skip ahead n\n                                // records.\n                                advanced = true;\n                                cursor.advance(n);\n                            } else {\n                                // When we get here, we've got the nth key.\n                                resolve(cursor.key);\n                            }\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openKeyCursor();\n                    var keys = [];\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (!cursor) {\n                            resolve(keys);\n                            return;\n                        }\n\n                        keys.push(cursor.key);\n                        cursor[\"continue\"]();\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n\n        var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n                forages[i]._dbInfo.db = db;\n            }\n            return db;\n        });\n\n        if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                }\n\n                var dropDBPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.deleteDatabase(options.name);\n\n                    req.onerror = function () {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        reject(req.error);\n                    };\n\n                    req.onblocked = function () {\n                        // Closing all open connections in onversionchange handler should prevent this situation, but if\n                        // we do get here, it just means the request remains pending - eventually it will succeed or error\n                        console.warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed');\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        resolve(db);\n                    };\n                });\n\n                return dropDBPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var i = 0; i < forages.length; i++) {\n                        var _forage = forages[i];\n                        _advanceReadiness(_forage._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        } else {\n            promise = dbPromise.then(function (db) {\n                if (!db.objectStoreNames.contains(options.storeName)) {\n                    return;\n                }\n\n                var newVersion = db.version + 1;\n\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                    forage._dbInfo.version = newVersion;\n                }\n\n                var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.open(options.name, newVersion);\n\n                    req.onerror = function (err) {\n                        var db = req.result;\n                        db.close();\n                        reject(err);\n                    };\n\n                    req.onupgradeneeded = function () {\n                        var db = req.result;\n                        db.deleteObjectStore(options.storeName);\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        db.close();\n                        resolve(db);\n                    };\n                });\n\n                return dropObjectPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var j = 0; j < forages.length; j++) {\n                        var _forage2 = forages[j];\n                        _forage2._dbInfo.db = db;\n                        _advanceReadiness(_forage2._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        }\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar asyncStorage = {\n    _driver: 'asyncStorage',\n    _initStorage: _initStorage,\n    _support: isIndexedDBValid(),\n    iterate: iterate,\n    getItem: getItem,\n    setItem: setItem,\n    removeItem: removeItem,\n    clear: clear,\n    length: length,\n    key: key,\n    keys: keys,\n    dropInstance: dropInstance\n};\n\nfunction isWebSQLValid() {\n    return typeof openDatabase === 'function';\n}\n\n// Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n// it to Base64, so this is how we store it to prevent very strange errors with less\n// verbose ways of binary <-> string data storage.\nvar BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nvar BLOB_TYPE_PREFIX = '~~local_forage_type~';\nvar BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n\nvar SERIALIZED_MARKER = '__lfsc__:';\nvar SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n// OMG the serializations!\nvar TYPE_ARRAYBUFFER = 'arbf';\nvar TYPE_BLOB = 'blob';\nvar TYPE_INT8ARRAY = 'si08';\nvar TYPE_UINT8ARRAY = 'ui08';\nvar TYPE_UINT8CLAMPEDARRAY = 'uic8';\nvar TYPE_INT16ARRAY = 'si16';\nvar TYPE_INT32ARRAY = 'si32';\nvar TYPE_UINT16ARRAY = 'ur16';\nvar TYPE_UINT32ARRAY = 'ui32';\nvar TYPE_FLOAT32ARRAY = 'fl32';\nvar TYPE_FLOAT64ARRAY = 'fl64';\nvar TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n\nvar toString$1 = Object.prototype.toString;\n\nfunction stringToBuffer(serializedString) {\n    // Fill the string into a ArrayBuffer.\n    var bufferLength = serializedString.length * 0.75;\n    var len = serializedString.length;\n    var i;\n    var p = 0;\n    var encoded1, encoded2, encoded3, encoded4;\n\n    if (serializedString[serializedString.length - 1] === '=') {\n        bufferLength--;\n        if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = new ArrayBuffer(bufferLength);\n    var bytes = new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n        encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n        encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n        encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n        /*jslint bitwise: true */\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n    return buffer;\n}\n\n// Converts a buffer to a string to store, serialized, in the backend\n// storage library.\nfunction bufferToString(buffer) {\n    // base64-arraybuffer\n    var bytes = new Uint8Array(buffer);\n    var base64String = '';\n    var i;\n\n    for (i = 0; i < bytes.length; i += 3) {\n        /*jslint bitwise: true */\n        base64String += BASE_CHARS[bytes[i] >> 2];\n        base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n        base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n        base64String += BASE_CHARS[bytes[i + 2] & 63];\n    }\n\n    if (bytes.length % 3 === 2) {\n        base64String = base64String.substring(0, base64String.length - 1) + '=';\n    } else if (bytes.length % 3 === 1) {\n        base64String = base64String.substring(0, base64String.length - 2) + '==';\n    }\n\n    return base64String;\n}\n\n// Serialize a value, afterwards executing a callback (which usually\n// instructs the `setItem()` callback/promise to be executed). This is how\n// we store binary data with localStorage.\nfunction serialize(value, callback) {\n    var valueType = '';\n    if (value) {\n        valueType = toString$1.call(value);\n    }\n\n    // Cannot use `value instanceof ArrayBuffer` or such here, as these\n    // checks fail when running the tests using casper.js...\n    //\n    // TODO: See why those tests fail and use a better solution.\n    if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n        // Convert binary arrays to a string and prefix the string with\n        // a special marker.\n        var buffer;\n        var marker = SERIALIZED_MARKER;\n\n        if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n        } else {\n            buffer = value.buffer;\n\n            if (valueType === '[object Int8Array]') {\n                marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n                marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n                marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n                marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n                marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n                marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n                marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n                marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n                marker += TYPE_FLOAT64ARRAY;\n            } else {\n                callback(new Error('Failed to get type for BinaryArray'));\n            }\n        }\n\n        callback(marker + bufferToString(buffer));\n    } else if (valueType === '[object Blob]') {\n        // Conver the blob to a binaryArray and then to a string.\n        var fileReader = new FileReader();\n\n        fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n        };\n\n        fileReader.readAsArrayBuffer(value);\n    } else {\n        try {\n            callback(JSON.stringify(value));\n        } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n\n            callback(null, e);\n        }\n    }\n}\n\n// Deserialize data we've inserted into a value column/field. We place\n// special markers into our strings to mark them as encoded; this isn't\n// as nice as a meta field, but it's the only sane thing we can do whilst\n// keeping localStorage support intact.\n//\n// Oftentimes this will just deserialize JSON content, but if we have a\n// special marker (SERIALIZED_MARKER, defined above), we will extract\n// some kind of arraybuffer/binary data/typed array out of the string.\nfunction deserialize(value) {\n    // If we haven't marked this string as being specially serialized (i.e.\n    // something other than serialized JSON), we can just return it and be\n    // done with it.\n    if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n        return JSON.parse(value);\n    }\n\n    // The following code deals with deserializing some kind of Blob or\n    // TypedArray. First we separate out the type of data we're dealing\n    // with from the data itself.\n    var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n    var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n\n    var blobType;\n    // Backwards-compatible blob type serialization strategy.\n    // DBs created with older versions of localForage will simply not have the blob type.\n    if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n        var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n        blobType = matcher[1];\n        serializedString = serializedString.substring(matcher[0].length);\n    }\n    var buffer = stringToBuffer(serializedString);\n\n    // Return the right type based on the code/type set during\n    // serialization.\n    switch (type) {\n        case TYPE_ARRAYBUFFER:\n            return buffer;\n        case TYPE_BLOB:\n            return createBlob([buffer], { type: blobType });\n        case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n        case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n        case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n        case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n        case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n        case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n        case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n        case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n        case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n        default:\n            throw new Error('Unkown type: ' + type);\n    }\n}\n\nvar localforageSerializer = {\n    serialize: serialize,\n    deserialize: deserialize,\n    stringToBuffer: stringToBuffer,\n    bufferToString: bufferToString\n};\n\n/*\n * Includes code from:\n *\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 Niklas von Hertzen\n * Licensed under the MIT license.\n */\n\nfunction createDbTable(t, dbInfo, callback, errorCallback) {\n    t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n}\n\n// Open the WebSQL database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage$1(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n        }\n    }\n\n    var dbInfoPromise = new Promise$1(function (resolve, reject) {\n        // Open the database; the openDatabase API will automatically\n        // create it for us if it doesn't exist.\n        try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n        } catch (e) {\n            return reject(e);\n        }\n\n        // Create our key/value table if it doesn't exist.\n        dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n                self._dbInfo = dbInfo;\n                resolve();\n            }, function (t, error) {\n                reject(error);\n            });\n        }, reject);\n    });\n\n    dbInfo.serializer = localforageSerializer;\n    return dbInfoPromise;\n}\n\nfunction tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n    t.executeSql(sqlStatement, args, callback, function (t, error) {\n        if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n                if (!results.rows.length) {\n                    // if the table is missing (was deleted)\n                    // re-create it table and retry\n                    createDbTable(t, dbInfo, function () {\n                        t.executeSql(sqlStatement, args, callback, errorCallback);\n                    }, errorCallback);\n                } else {\n                    errorCallback(t, error);\n                }\n            }, errorCallback);\n        } else {\n            errorCallback(t, error);\n        }\n    }, errorCallback);\n}\n\nfunction getItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).value : null;\n\n                    // Check to see if this is serialized content we need to\n                    // unpack.\n                    if (result) {\n                        result = dbInfo.serializer.deserialize(result);\n                    }\n\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction iterate$1(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var rows = results.rows;\n                    var length = rows.length;\n\n                    for (var i = 0; i < length; i++) {\n                        var item = rows.item(i);\n                        var result = item.value;\n\n                        // Check to see if this is serialized content\n                        // we need to unpack.\n                        if (result) {\n                            result = dbInfo.serializer.deserialize(result);\n                        }\n\n                        result = iterator(result, item.key, i + 1);\n\n                        // void(0) prevents problems with redefinition\n                        // of `undefined`.\n                        if (result !== void 0) {\n                            resolve(result);\n                            return;\n                        }\n                    }\n\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction _setItem(key, value, callback, retriesLeft) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n                value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    dbInfo.db.transaction(function (t) {\n                        tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                            resolve(originalValue);\n                        }, function (t, error) {\n                            reject(error);\n                        });\n                    }, function (sqlError) {\n                        // The transaction failed; check\n                        // to see if it's a quota error.\n                        if (sqlError.code === sqlError.QUOTA_ERR) {\n                            // We reject the callback outright for now, but\n                            // it's worth trying to re-run the transaction.\n                            // Even if the user accepts the prompt to use\n                            // more storage on Safari, this error will\n                            // be called.\n                            //\n                            // Try to re-run the transaction.\n                            if (retriesLeft > 0) {\n                                resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                                return;\n                            }\n                            reject(sqlError);\n                        }\n                    });\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction setItem$1(key, value, callback) {\n    return _setItem.apply(this, [key, value, callback, 1]);\n}\n\nfunction removeItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Deletes every item in the table.\n// TODO: Find out if this resets the AUTO_INCREMENT number.\nfunction clear$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Does a simple `COUNT(key)` to get the number of items stored in\n// localForage.\nfunction length$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                // Ahhh, SQL makes this one soooooo easy.\n                tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var result = results.rows.item(0).c;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Return the key located at key index X; essentially gets the key from a\n// `WHERE id = ?`. This is the most efficient way I can think to implement\n// this rarely-used (in my experience) part of the API, but it can seem\n// inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n// the ID of each key will change every time it's updated. Perhaps a stored\n// procedure for the `setItem()` SQL would solve this problem?\n// TODO: Don't change ID on `setItem()`.\nfunction key$1(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).key : null;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var keys = [];\n\n                    for (var i = 0; i < results.rows.length; i++) {\n                        keys.push(results.rows.item(i).key);\n                    }\n\n                    resolve(keys);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// https://www.w3.org/TR/webdatabase/#databases\n// > There is no way to enumerate or delete the databases available for an origin from this API.\nfunction getAllStoreNames(db) {\n    return new Promise$1(function (resolve, reject) {\n        db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n                var storeNames = [];\n\n                for (var i = 0; i < results.rows.length; i++) {\n                    storeNames.push(results.rows.item(i).name);\n                }\n\n                resolve({\n                    db: db,\n                    storeNames: storeNames\n                });\n            }, function (t, error) {\n                reject(error);\n            });\n        }, function (sqlError) {\n            reject(sqlError);\n        });\n    });\n}\n\nfunction dropInstance$1(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n                // use the db reference of the current instance\n                db = self._dbInfo.db;\n            } else {\n                db = openDatabase(options.name, '', '', 0);\n            }\n\n            if (!options.storeName) {\n                // drop all database tables\n                resolve(getAllStoreNames(db));\n            } else {\n                resolve({\n                    db: db,\n                    storeNames: [options.storeName]\n                });\n            }\n        }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n                operationInfo.db.transaction(function (t) {\n                    function dropTable(storeName) {\n                        return new Promise$1(function (resolve, reject) {\n                            t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                                resolve();\n                            }, function (t, error) {\n                                reject(error);\n                            });\n                        });\n                    }\n\n                    var operations = [];\n                    for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                        operations.push(dropTable(operationInfo.storeNames[i]));\n                    }\n\n                    Promise$1.all(operations).then(function () {\n                        resolve();\n                    })[\"catch\"](function (e) {\n                        reject(e);\n                    });\n                }, function (sqlError) {\n                    reject(sqlError);\n                });\n            });\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar webSQLStorage = {\n    _driver: 'webSQLStorage',\n    _initStorage: _initStorage$1,\n    _support: isWebSQLValid(),\n    iterate: iterate$1,\n    getItem: getItem$1,\n    setItem: setItem$1,\n    removeItem: removeItem$1,\n    clear: clear$1,\n    length: length$1,\n    key: key$1,\n    keys: keys$1,\n    dropInstance: dropInstance$1\n};\n\nfunction isLocalStorageValid() {\n    try {\n        return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n        // in IE8 typeof localStorage.setItem === 'object'\n        !!localStorage.setItem;\n    } catch (e) {\n        return false;\n    }\n}\n\nfunction _getKeyPrefix(options, defaultConfig) {\n    var keyPrefix = options.name + '/';\n\n    if (options.storeName !== defaultConfig.storeName) {\n        keyPrefix += options.storeName + '/';\n    }\n    return keyPrefix;\n}\n\n// Check if localStorage throws when saving an item\nfunction checkIfLocalStorageThrows() {\n    var localStorageTestKey = '_localforage_support_test';\n\n    try {\n        localStorage.setItem(localStorageTestKey, true);\n        localStorage.removeItem(localStorageTestKey);\n\n        return false;\n    } catch (e) {\n        return true;\n    }\n}\n\n// Check if localStorage is usable and allows to save an item\n// This method checks if localStorage is usable in Safari Private Browsing\n// mode, or in any other case where the available quota for localStorage\n// is 0 and there wasn't any saved items yet.\nfunction _isLocalStorageUsable() {\n    return !checkIfLocalStorageThrows() || localStorage.length > 0;\n}\n\n// Config the localStorage backend, using options set in the config.\nfunction _initStorage$2(options) {\n    var self = this;\n    var dbInfo = {};\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n\n    if (!_isLocalStorageUsable()) {\n        return Promise$1.reject();\n    }\n\n    self._dbInfo = dbInfo;\n    dbInfo.serializer = localforageSerializer;\n\n    return Promise$1.resolve();\n}\n\n// Remove all keys from the datastore, effectively destroying all data in\n// the app's key/value store!\nfunction clear$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var keyPrefix = self._dbInfo.keyPrefix;\n\n        for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n\n            if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Retrieve an item from the store. Unlike the original async_storage\n// library in Gaia, we don't modify return values at all. If a key's value\n// is `undefined`, we pass that value to the callback function.\nfunction getItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n        // If a result was found, parse it from the serialized\n        // string into a JS object. If result isn't truthy, the key\n        // is likely undefined and we'll pass it straight to the\n        // callback.\n        if (result) {\n            result = dbInfo.serializer.deserialize(result);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items in the store.\nfunction iterate$2(iterator, callback) {\n    var self = this;\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var keyPrefix = dbInfo.keyPrefix;\n        var keyPrefixLength = keyPrefix.length;\n        var length = localStorage.length;\n\n        // We use a dedicated iterator instead of the `i` variable below\n        // so other keys we fetch in localStorage aren't counted in\n        // the `iterationNumber` argument passed to the `iterate()`\n        // callback.\n        //\n        // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n        var iterationNumber = 1;\n\n        for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n                continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n                value = dbInfo.serializer.deserialize(value);\n            }\n\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n\n            if (value !== void 0) {\n                return value;\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Same as localStorage's key() method, except takes a callback.\nfunction key$2(n, callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result;\n        try {\n            result = localStorage.key(n);\n        } catch (error) {\n            result = null;\n        }\n\n        // Remove the prefix from the key, if a key is found.\n        if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var length = localStorage.length;\n        var keys = [];\n\n        for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n                keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n        }\n\n        return keys;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Supply the number of keys in the datastore to the callback function.\nfunction length$2(callback) {\n    var self = this;\n    var promise = self.keys().then(function (keys) {\n        return keys.length;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Remove an item from the store, nice and simple.\nfunction removeItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        localStorage.removeItem(dbInfo.keyPrefix + key);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Set a key's value and run an optional callback once the value is set.\n// Unlike Gaia's implementation, the callback function is passed the value,\n// in case you want to operate on that value only after you're sure it\n// saved, or something like that.\nfunction setItem$2(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        // Convert undefined values to null.\n        // https://github.com/mozilla/localForage/pull/42\n        if (value === undefined) {\n            value = null;\n        }\n\n        // Save the original value to pass to the callback.\n        var originalValue = value;\n\n        return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    try {\n                        localStorage.setItem(dbInfo.keyPrefix + key, value);\n                        resolve(originalValue);\n                    } catch (e) {\n                        // localStorage capacity exceeded.\n                        // TODO: Make this a specific error/event.\n                        if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                            reject(e);\n                        }\n                        reject(e);\n                    }\n                }\n            });\n        });\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance$2(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        var currentConfig = this.config();\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n                resolve(options.name + '/');\n            } else {\n                resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n        }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n                var key = localStorage.key(i);\n\n                if (key.indexOf(keyPrefix) === 0) {\n                    localStorage.removeItem(key);\n                }\n            }\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar localStorageWrapper = {\n    _driver: 'localStorageWrapper',\n    _initStorage: _initStorage$2,\n    _support: isLocalStorageValid(),\n    iterate: iterate$2,\n    getItem: getItem$2,\n    setItem: setItem$2,\n    removeItem: removeItem$2,\n    clear: clear$2,\n    length: length$2,\n    key: key$2,\n    keys: keys$2,\n    dropInstance: dropInstance$2\n};\n\nvar sameValue = function sameValue(x, y) {\n    return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n};\n\nvar includes = function includes(array, searchElement) {\n    var len = array.length;\n    var i = 0;\n    while (i < len) {\n        if (sameValue(array[i], searchElement)) {\n            return true;\n        }\n        i++;\n    }\n\n    return false;\n};\n\nvar isArray = Array.isArray || function (arg) {\n    return Object.prototype.toString.call(arg) === '[object Array]';\n};\n\n// Drivers are stored here when `defineDriver()` is called.\n// They are shared across all instances of localForage.\nvar DefinedDrivers = {};\n\nvar DriverSupport = {};\n\nvar DefaultDrivers = {\n    INDEXEDDB: asyncStorage,\n    WEBSQL: webSQLStorage,\n    LOCALSTORAGE: localStorageWrapper\n};\n\nvar DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n\nvar OptionalDriverMethods = ['dropInstance'];\n\nvar LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n\nvar DefaultConfig = {\n    description: '',\n    driver: DefaultDriverOrder.slice(),\n    name: 'localforage',\n    // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n    // we can use without a prompt.\n    size: 4980736,\n    storeName: 'keyvaluepairs',\n    version: 1.0\n};\n\nfunction callWhenReady(localForageInstance, libraryMethod) {\n    localForageInstance[libraryMethod] = function () {\n        var _args = arguments;\n        return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n        });\n    };\n}\n\nfunction extend() {\n    for (var i = 1; i < arguments.length; i++) {\n        var arg = arguments[i];\n\n        if (arg) {\n            for (var _key in arg) {\n                if (arg.hasOwnProperty(_key)) {\n                    if (isArray(arg[_key])) {\n                        arguments[0][_key] = arg[_key].slice();\n                    } else {\n                        arguments[0][_key] = arg[_key];\n                    }\n                }\n            }\n        }\n    }\n\n    return arguments[0];\n}\n\nvar LocalForage = function () {\n    function LocalForage(options) {\n        _classCallCheck(this, LocalForage);\n\n        for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n                var driver = DefaultDrivers[driverTypeKey];\n                var driverName = driver._driver;\n                this[driverTypeKey] = driverName;\n\n                if (!DefinedDrivers[driverName]) {\n                    // we don't need to wait for the promise,\n                    // since the default drivers can be defined\n                    // in a blocking manner\n                    this.defineDriver(driver);\n                }\n            }\n        }\n\n        this._defaultConfig = extend({}, DefaultConfig);\n        this._config = extend({}, this._defaultConfig, options);\n        this._driverSet = null;\n        this._initDriver = null;\n        this._ready = false;\n        this._dbInfo = null;\n\n        this._wrapLibraryMethodsWithReady();\n        this.setDriver(this._config.driver)[\"catch\"](function () {});\n    }\n\n    // Set any config values for localForage; can be called anytime before\n    // the first API call (e.g. `getItem`, `setItem`).\n    // We loop through options so we don't overwrite existing config\n    // values.\n\n\n    LocalForage.prototype.config = function config(options) {\n        // If the options argument is an object, we use it to set values.\n        // Otherwise, we return either a specified config value or all\n        // config values.\n        if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n                return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n\n            for (var i in options) {\n                if (i === 'storeName') {\n                    options[i] = options[i].replace(/\\W/g, '_');\n                }\n\n                if (i === 'version' && typeof options[i] !== 'number') {\n                    return new Error('Database version must be a number.');\n                }\n\n                this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n                return this.setDriver(this._config.driver);\n            }\n\n            return true;\n        } else if (typeof options === 'string') {\n            return this._config[options];\n        } else {\n            return this._config;\n        }\n    };\n\n    // Used to define a custom driver, shared across all instances of\n    // localForage.\n\n\n    LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n        var promise = new Promise$1(function (resolve, reject) {\n            try {\n                var driverName = driverObject._driver;\n                var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n                // A driver name should be defined and not overlap with the\n                // library-defined, default drivers.\n                if (!driverObject._driver) {\n                    reject(complianceError);\n                    return;\n                }\n\n                var driverMethods = LibraryMethods.concat('_initStorage');\n                for (var i = 0, len = driverMethods.length; i < len; i++) {\n                    var driverMethodName = driverMethods[i];\n\n                    // when the property is there,\n                    // it should be a method even when optional\n                    var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                    if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                        reject(complianceError);\n                        return;\n                    }\n                }\n\n                var configureMissingMethods = function configureMissingMethods() {\n                    var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                        return function () {\n                            var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                            var promise = Promise$1.reject(error);\n                            executeCallback(promise, arguments[arguments.length - 1]);\n                            return promise;\n                        };\n                    };\n\n                    for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                        var optionalDriverMethod = OptionalDriverMethods[_i];\n                        if (!driverObject[optionalDriverMethod]) {\n                            driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                        }\n                    }\n                };\n\n                configureMissingMethods();\n\n                var setDriverSupport = function setDriverSupport(support) {\n                    if (DefinedDrivers[driverName]) {\n                        console.info('Redefining LocalForage driver: ' + driverName);\n                    }\n                    DefinedDrivers[driverName] = driverObject;\n                    DriverSupport[driverName] = support;\n                    // don't use a then, so that we can define\n                    // drivers that have simple _support methods\n                    // in a blocking manner\n                    resolve();\n                };\n\n                if ('_support' in driverObject) {\n                    if (driverObject._support && typeof driverObject._support === 'function') {\n                        driverObject._support().then(setDriverSupport, reject);\n                    } else {\n                        setDriverSupport(!!driverObject._support);\n                    }\n                } else {\n                    setDriverSupport(true);\n                }\n            } catch (e) {\n                reject(e);\n            }\n        });\n\n        executeTwoCallbacks(promise, callback, errorCallback);\n        return promise;\n    };\n\n    LocalForage.prototype.driver = function driver() {\n        return this._driver || null;\n    };\n\n    LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n        var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n\n        executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n        return getDriverPromise;\n    };\n\n    LocalForage.prototype.getSerializer = function getSerializer(callback) {\n        var serializerPromise = Promise$1.resolve(localforageSerializer);\n        executeTwoCallbacks(serializerPromise, callback);\n        return serializerPromise;\n    };\n\n    LocalForage.prototype.ready = function ready(callback) {\n        var self = this;\n\n        var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n                self._ready = self._initDriver();\n            }\n\n            return self._ready;\n        });\n\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n    };\n\n    LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n        var self = this;\n\n        if (!isArray(drivers)) {\n            drivers = [drivers];\n        }\n\n        var supportedDrivers = this._getSupportedDrivers(drivers);\n\n        function setDriverToConfig() {\n            self._config.driver = self.driver();\n        }\n\n        function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n        }\n\n        function initDriver(supportedDrivers) {\n            return function () {\n                var currentDriverIndex = 0;\n\n                function driverPromiseLoop() {\n                    while (currentDriverIndex < supportedDrivers.length) {\n                        var driverName = supportedDrivers[currentDriverIndex];\n                        currentDriverIndex++;\n\n                        self._dbInfo = null;\n                        self._ready = null;\n\n                        return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                    }\n\n                    setDriverToConfig();\n                    var error = new Error('No available storage method found.');\n                    self._driverSet = Promise$1.reject(error);\n                    return self._driverSet;\n                }\n\n                return driverPromiseLoop();\n            };\n        }\n\n        // There might be a driver initialization in progress\n        // so wait for it to finish in order to avoid a possible\n        // race condition to set _dbInfo\n        var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n        }) : Promise$1.resolve();\n\n        this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n\n            return self.getDriver(driverName).then(function (driver) {\n                self._driver = driver._driver;\n                setDriverToConfig();\n                self._wrapLibraryMethodsWithReady();\n                self._initDriver = initDriver(supportedDrivers);\n            });\n        })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n        });\n\n        executeTwoCallbacks(this._driverSet, callback, errorCallback);\n        return this._driverSet;\n    };\n\n    LocalForage.prototype.supports = function supports(driverName) {\n        return !!DriverSupport[driverName];\n    };\n\n    LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n        extend(this, libraryMethodsAndProperties);\n    };\n\n    LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n        var supportedDrivers = [];\n        for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n                supportedDrivers.push(driverName);\n            }\n        }\n        return supportedDrivers;\n    };\n\n    LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n        // Add a stub for each driver API method that delays the call to the\n        // corresponding driver method until localForage is ready. These stubs\n        // will be replaced by the driver methods as soon as the driver is\n        // loaded, so there is no performance impact.\n        for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n        }\n    };\n\n    LocalForage.prototype.createInstance = function createInstance(options) {\n        return new LocalForage(options);\n    };\n\n    return LocalForage;\n}();\n\n// The actual localForage object that we expose as a module or via a\n// global. It's extended by pulling in one of our other libraries.\n\n\nvar localforage_js = new LocalForage();\n\nmodule.exports = localforage_js;\n\n},{\"3\":3}]},{},[4])(4)\n});\n"], "mappings": ";;;;;;AAAA;AAAA;AAMA,KAAC,SAAS,GAAE;AAAC,UAAG,OAAO,YAAU,YAAU,OAAO,WAAS,aAAY;AAAC,eAAO,UAAQ,EAAE;AAAA,MAAC,WAAS,OAAO,WAAS,cAAY,OAAO,KAAI;AAAC,eAAO,CAAC,GAAE,CAAC;AAAA,MAAC,OAAK;AAAC,YAAI;AAAE,YAAG,OAAO,WAAS,aAAY;AAAC,cAAE;AAAA,QAAM,WAAS,OAAO,WAAS,aAAY;AAAC,cAAE;AAAA,QAAM,WAAS,OAAO,SAAO,aAAY;AAAC,cAAE;AAAA,QAAI,OAAK;AAAC,cAAE;AAAA,QAAI;AAAC,UAAE,cAAc,EAAE;AAAA,MAAC;AAAA,IAAC,GAAG,WAAU;AAAC,UAAIA,SAAOC,SAAOC;AAAQ,aAAQ,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,iBAAS,EAAEC,IAAE,GAAE;AAAC,cAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,gBAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,kBAAI,IAAE,OAAO,aAAS,cAAY;AAAQ,kBAAG,CAAC,KAAG;AAAE,uBAAO,EAAEA,IAAE,IAAE;AAAE,kBAAG;AAAE,uBAAO,EAAEA,IAAE,IAAE;AAAE,kBAAI,IAAE,IAAI,MAAM,yBAAuBA,KAAE,GAAG;AAAE,oBAAO,EAAE,OAAK,oBAAoB;AAAA,YAAE;AAAC,gBAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,cAAEA,EAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAQ,SAASC,IAAE;AAAC,kBAAIC,KAAE,EAAEF,EAAC,EAAE,CAAC,EAAEC,EAAC;AAAE,qBAAO,EAAEC,KAAEA,KAAED,EAAC;AAAA,YAAC,GAAE,GAAE,EAAE,SAAQ,GAAE,GAAE,GAAE,CAAC;AAAA,UAAC;AAAC,iBAAO,EAAED,EAAC,EAAE;AAAA,QAAO;AAAC,YAAI,IAAE,OAAO,aAAS,cAAY;AAAQ,iBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,YAAE,EAAE,CAAC,CAAC;AAAE,eAAO;AAAA,MAAC,EAAG,EAAC,GAAE,CAAC,SAAS,SAAQF,SAAOC,UAAQ;AACr1B,SAAC,SAAUI,SAAO;AAClB;AACA,cAAI,WAAWA,QAAO,oBAAoBA,QAAO;AAEjD,cAAI;AAEJ;AACE,gBAAI,UAAU;AACZ,kBAAI,SAAS;AACb,kBAAI,WAAW,IAAI,SAAS,QAAQ;AACpC,kBAAI,UAAUA,QAAO,SAAS,eAAe,EAAE;AAC/C,uBAAS,QAAQ,SAAS;AAAA,gBACxB,eAAe;AAAA,cACjB,CAAC;AACD,8BAAgB,WAAY;AAC1B,wBAAQ,OAAQ,SAAS,EAAE,SAAS;AAAA,cACtC;AAAA,YACF,WAAW,CAACA,QAAO,gBAAgB,OAAOA,QAAO,mBAAmB,aAAa;AAC/E,kBAAI,UAAU,IAAIA,QAAO,eAAe;AACxC,sBAAQ,MAAM,YAAY;AAC1B,8BAAgB,WAAY;AAC1B,wBAAQ,MAAM,YAAY,CAAC;AAAA,cAC7B;AAAA,YACF,WAAW,cAAcA,WAAU,wBAAwBA,QAAO,SAAS,cAAc,QAAQ,GAAG;AAClG,8BAAgB,WAAY;AAI1B,oBAAI,WAAWA,QAAO,SAAS,cAAc,QAAQ;AACrD,yBAAS,qBAAqB,WAAY;AACxC,2BAAS;AAET,2BAAS,qBAAqB;AAC9B,2BAAS,WAAW,YAAY,QAAQ;AACxC,6BAAW;AAAA,gBACb;AACA,gBAAAA,QAAO,SAAS,gBAAgB,YAAY,QAAQ;AAAA,cACtD;AAAA,YACF,OAAO;AACL,8BAAgB,WAAY;AAC1B,2BAAW,UAAU,CAAC;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AAEA,cAAI;AACJ,cAAI,QAAQ,CAAC;AAEb,mBAAS,WAAW;AAClB,uBAAW;AACX,gBAAI,GAAG;AACP,gBAAI,MAAM,MAAM;AAChB,mBAAO,KAAK;AACV,yBAAW;AACX,sBAAQ,CAAC;AACT,kBAAI;AACJ,qBAAO,EAAE,IAAI,KAAK;AAChB,yBAAS,CAAC,EAAE;AAAA,cACd;AACA,oBAAM,MAAM;AAAA,YACd;AACA,uBAAW;AAAA,UACb;AAEA,UAAAL,QAAO,UAAU;AACjB,mBAAS,UAAU,MAAM;AACvB,gBAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,UAAU;AACvC,4BAAc;AAAA,YAChB;AAAA,UACF;AAAA,QAEA,GAAG,KAAK,MAAK,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAAA,MACtI,GAAE,CAAC,CAAC,GAAE,GAAE,CAAC,SAAS,SAAQA,SAAOC,UAAQ;AACzC;AACA,YAAI,YAAY,QAAQ,CAAC;AAGzB,iBAAS,WAAW;AAAA,QAAC;AAErB,YAAI,WAAW,CAAC;AAEhB,YAAI,WAAW,CAAC,UAAU;AAC1B,YAAI,YAAY,CAAC,WAAW;AAC5B,YAAI,UAAU,CAAC,SAAS;AAExB,QAAAD,QAAO,UAAUM;AAEjB,iBAASA,SAAQ,UAAU;AACzB,cAAI,OAAO,aAAa,YAAY;AAClC,kBAAM,IAAI,UAAU,6BAA6B;AAAA,UACnD;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ,CAAC;AACd,eAAK,UAAU;AACf,cAAI,aAAa,UAAU;AACzB,kCAAsB,MAAM,QAAQ;AAAA,UACtC;AAAA,QACF;AAEA,QAAAA,SAAQ,UAAU,OAAO,IAAI,SAAU,YAAY;AACjD,iBAAO,KAAK,KAAK,MAAM,UAAU;AAAA,QACnC;AACA,QAAAA,SAAQ,UAAU,OAAO,SAAU,aAAa,YAAY;AAC1D,cAAI,OAAO,gBAAgB,cAAc,KAAK,UAAU,aACtD,OAAO,eAAe,cAAc,KAAK,UAAU,UAAU;AAC7D,mBAAO;AAAA,UACT;AACA,cAAI,UAAU,IAAI,KAAK,YAAY,QAAQ;AAC3C,cAAI,KAAK,UAAU,SAAS;AAC1B,gBAAI,WAAW,KAAK,UAAU,YAAY,cAAc;AACxD,mBAAO,SAAS,UAAU,KAAK,OAAO;AAAA,UACxC,OAAO;AACL,iBAAK,MAAM,KAAK,IAAI,UAAU,SAAS,aAAa,UAAU,CAAC;AAAA,UACjE;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,UAAU,SAAS,aAAa,YAAY;AACnD,eAAK,UAAU;AACf,cAAI,OAAO,gBAAgB,YAAY;AACrC,iBAAK,cAAc;AACnB,iBAAK,gBAAgB,KAAK;AAAA,UAC5B;AACA,cAAI,OAAO,eAAe,YAAY;AACpC,iBAAK,aAAa;AAClB,iBAAK,eAAe,KAAK;AAAA,UAC3B;AAAA,QACF;AACA,kBAAU,UAAU,gBAAgB,SAAU,OAAO;AACnD,mBAAS,QAAQ,KAAK,SAAS,KAAK;AAAA,QACtC;AACA,kBAAU,UAAU,qBAAqB,SAAU,OAAO;AACxD,iBAAO,KAAK,SAAS,KAAK,aAAa,KAAK;AAAA,QAC9C;AACA,kBAAU,UAAU,eAAe,SAAU,OAAO;AAClD,mBAAS,OAAO,KAAK,SAAS,KAAK;AAAA,QACrC;AACA,kBAAU,UAAU,oBAAoB,SAAU,OAAO;AACvD,iBAAO,KAAK,SAAS,KAAK,YAAY,KAAK;AAAA,QAC7C;AAEA,iBAAS,OAAO,SAAS,MAAM,OAAO;AACpC,oBAAU,WAAY;AACpB,gBAAI;AACJ,gBAAI;AACF,4BAAc,KAAK,KAAK;AAAA,YAC1B,SAAS,GAAG;AACV,qBAAO,SAAS,OAAO,SAAS,CAAC;AAAA,YACnC;AACA,gBAAI,gBAAgB,SAAS;AAC3B,uBAAS,OAAO,SAAS,IAAI,UAAU,oCAAoC,CAAC;AAAA,YAC9E,OAAO;AACL,uBAAS,QAAQ,SAAS,WAAW;AAAA,YACvC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,UAAU,SAAUC,OAAM,OAAO;AACxC,cAAI,SAAS,SAAS,SAAS,KAAK;AACpC,cAAI,OAAO,WAAW,SAAS;AAC7B,mBAAO,SAAS,OAAOA,OAAM,OAAO,KAAK;AAAA,UAC3C;AACA,cAAI,WAAW,OAAO;AAEtB,cAAI,UAAU;AACZ,kCAAsBA,OAAM,QAAQ;AAAA,UACtC,OAAO;AACL,YAAAA,MAAK,QAAQ;AACb,YAAAA,MAAK,UAAU;AACf,gBAAI,IAAI;AACR,gBAAI,MAAMA,MAAK,MAAM;AACrB,mBAAO,EAAE,IAAI,KAAK;AAChB,cAAAA,MAAK,MAAM,CAAC,EAAE,cAAc,KAAK;AAAA,YACnC;AAAA,UACF;AACA,iBAAOA;AAAA,QACT;AACA,iBAAS,SAAS,SAAUA,OAAM,OAAO;AACvC,UAAAA,MAAK,QAAQ;AACb,UAAAA,MAAK,UAAU;AACf,cAAI,IAAI;AACR,cAAI,MAAMA,MAAK,MAAM;AACrB,iBAAO,EAAE,IAAI,KAAK;AAChB,YAAAA,MAAK,MAAM,CAAC,EAAE,aAAa,KAAK;AAAA,UAClC;AACA,iBAAOA;AAAA,QACT;AAEA,iBAAS,QAAQ,KAAK;AAEpB,cAAI,OAAO,OAAO,IAAI;AACtB,cAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,eAAe,OAAO,SAAS,YAAY;AAC/F,mBAAO,SAAS,WAAW;AACzB,mBAAK,MAAM,KAAK,SAAS;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,sBAAsBA,OAAM,UAAU;AAE7C,cAAI,SAAS;AACb,mBAAS,QAAQ,OAAO;AACtB,gBAAI,QAAQ;AACV;AAAA,YACF;AACA,qBAAS;AACT,qBAAS,OAAOA,OAAM,KAAK;AAAA,UAC7B;AAEA,mBAAS,UAAU,OAAO;AACxB,gBAAI,QAAQ;AACV;AAAA,YACF;AACA,qBAAS;AACT,qBAAS,QAAQA,OAAM,KAAK;AAAA,UAC9B;AAEA,mBAAS,cAAc;AACrB,qBAAS,WAAW,OAAO;AAAA,UAC7B;AAEA,cAAI,SAAS,SAAS,WAAW;AACjC,cAAI,OAAO,WAAW,SAAS;AAC7B,oBAAQ,OAAO,KAAK;AAAA,UACtB;AAAA,QACF;AAEA,iBAAS,SAAS,MAAM,OAAO;AAC7B,cAAI,MAAM,CAAC;AACX,cAAI;AACF,gBAAI,QAAQ,KAAK,KAAK;AACtB,gBAAI,SAAS;AAAA,UACf,SAAS,GAAG;AACV,gBAAI,SAAS;AACb,gBAAI,QAAQ;AAAA,UACd;AACA,iBAAO;AAAA,QACT;AAEA,QAAAD,SAAQ,UAAU;AAClB,iBAAS,QAAQ,OAAO;AACtB,cAAI,iBAAiB,MAAM;AACzB,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS,QAAQ,IAAI,KAAK,QAAQ,GAAG,KAAK;AAAA,QACnD;AAEA,QAAAA,SAAQ,SAAS;AACjB,iBAAS,OAAO,QAAQ;AACtB,cAAI,UAAU,IAAI,KAAK,QAAQ;AAC/B,iBAAO,SAAS,OAAO,SAAS,MAAM;AAAA,QACxC;AAEA,QAAAA,SAAQ,MAAM;AACd,iBAAS,IAAI,UAAU;AACrB,cAAIC,QAAO;AACX,cAAI,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAkB;AACjE,mBAAO,KAAK,OAAO,IAAI,UAAU,kBAAkB,CAAC;AAAA,UACtD;AAEA,cAAI,MAAM,SAAS;AACnB,cAAI,SAAS;AACb,cAAI,CAAC,KAAK;AACR,mBAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,UACxB;AAEA,cAAI,SAAS,IAAI,MAAM,GAAG;AAC1B,cAAI,WAAW;AACf,cAAI,IAAI;AACR,cAAI,UAAU,IAAI,KAAK,QAAQ;AAE/B,iBAAO,EAAE,IAAI,KAAK;AAChB,wBAAY,SAAS,CAAC,GAAG,CAAC;AAAA,UAC5B;AACA,iBAAO;AACP,mBAAS,YAAY,OAAOC,IAAG;AAC7B,YAAAD,MAAK,QAAQ,KAAK,EAAE,KAAK,gBAAgB,SAAU,OAAO;AACxD,kBAAI,CAAC,QAAQ;AACX,yBAAS;AACT,yBAAS,OAAO,SAAS,KAAK;AAAA,cAChC;AAAA,YACF,CAAC;AACD,qBAAS,eAAe,UAAU;AAChC,qBAAOC,EAAC,IAAI;AACZ,kBAAI,EAAE,aAAa,OAAO,CAAC,QAAQ;AACjC,yBAAS;AACT,yBAAS,QAAQ,SAAS,MAAM;AAAA,cAClC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,QAAAF,SAAQ,OAAO;AACf,iBAAS,KAAK,UAAU;AACtB,cAAIC,QAAO;AACX,cAAI,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAkB;AACjE,mBAAO,KAAK,OAAO,IAAI,UAAU,kBAAkB,CAAC;AAAA,UACtD;AAEA,cAAI,MAAM,SAAS;AACnB,cAAI,SAAS;AACb,cAAI,CAAC,KAAK;AACR,mBAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,UACxB;AAEA,cAAI,IAAI;AACR,cAAI,UAAU,IAAI,KAAK,QAAQ;AAE/B,iBAAO,EAAE,IAAI,KAAK;AAChB,qBAAS,SAAS,CAAC,CAAC;AAAA,UACtB;AACA,iBAAO;AACP,mBAAS,SAAS,OAAO;AACvB,YAAAA,MAAK,QAAQ,KAAK,EAAE,KAAK,SAAU,UAAU;AAC3C,kBAAI,CAAC,QAAQ;AACX,yBAAS;AACT,yBAAS,QAAQ,SAAS,QAAQ;AAAA,cACpC;AAAA,YACF,GAAG,SAAU,OAAO;AAClB,kBAAI,CAAC,QAAQ;AACX,yBAAS;AACT,yBAAS,OAAO,SAAS,KAAK;AAAA,cAChC;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MAEA,GAAE,EAAC,KAAI,EAAC,CAAC,GAAE,GAAE,CAAC,SAAS,SAAQP,SAAOC,UAAQ;AAC9C,SAAC,SAAUI,SAAO;AAClB;AACA,cAAI,OAAOA,QAAO,YAAY,YAAY;AACxC,YAAAA,QAAO,UAAU,QAAQ,CAAC;AAAA,UAC5B;AAAA,QAEA,GAAG,KAAK,MAAK,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAAA,MACtI,GAAE,EAAC,KAAI,EAAC,CAAC,GAAE,GAAE,CAAC,SAAS,SAAQL,SAAOC,UAAQ;AAC9C;AAEA,YAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,iBAAO,OAAO;AAAA,QAAK,IAAI,SAAU,KAAK;AAAE,iBAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,QAAK;AAE3Q,iBAAS,gBAAgB,UAAU,aAAa;AAAE,cAAI,EAAE,oBAAoB,cAAc;AAAE,kBAAM,IAAI,UAAU,mCAAmC;AAAA,UAAG;AAAA,QAAE;AAExJ,iBAAS,SAAS;AAEd,cAAI;AACA,gBAAI,OAAO,cAAc,aAAa;AAClC,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,oBAAoB,aAAa;AACxC,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,iBAAiB,aAAa;AACrC,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,eAAe,aAAa;AACnC,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,gBAAgB,aAAa;AACpC,qBAAO;AAAA,YACX;AAAA,UACJ,SAAS,GAAG;AACR;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,MAAM,OAAO;AAEjB,iBAAS,mBAAmB;AACxB,cAAI;AAGA,gBAAI,CAAC,OAAO,CAAC,IAAI,MAAM;AACnB,qBAAO;AAAA,YACX;AAKA,gBAAI,WAAW,OAAO,iBAAiB,eAAe,4BAA4B,KAAK,UAAU,SAAS,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,KAAK,CAAC,aAAa,KAAK,UAAU,QAAQ;AAE3L,gBAAI,WAAW,OAAO,UAAU,cAAc,MAAM,SAAS,EAAE,QAAQ,cAAc,MAAM;AAQ3F,oBAAQ,CAAC,YAAY,aAAa,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA,YAKvD,OAAO,gBAAgB;AAAA,UAC3B,SAAS,GAAG;AACR,mBAAO;AAAA,UACX;AAAA,QACJ;AAQA,iBAAS,WAAW,OAAO,YAAY;AAEnC,kBAAQ,SAAS,CAAC;AAClB,uBAAa,cAAc,CAAC;AAC5B,cAAI;AACA,mBAAO,IAAI,KAAK,OAAO,UAAU;AAAA,UACrC,SAAS,GAAG;AACR,gBAAI,EAAE,SAAS,aAAa;AACxB,oBAAM;AAAA,YACV;AACA,gBAAI,UAAU,OAAO,gBAAgB,cAAc,cAAc,OAAO,kBAAkB,cAAc,gBAAgB,OAAO,mBAAmB,cAAc,iBAAiB;AACjL,gBAAI,UAAU,IAAI,QAAQ;AAC1B,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACtC,sBAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,YAC3B;AACA,mBAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,UAC1C;AAAA,QACJ;AAIA,YAAI,OAAO,YAAY,aAAa;AAGhC,kBAAQ,CAAC;AAAA,QACb;AACA,YAAI,YAAY;AAEhB,iBAAS,gBAAgB,SAAS,UAAU;AACxC,cAAI,UAAU;AACV,oBAAQ,KAAK,SAAU,QAAQ;AAC3B,uBAAS,MAAM,MAAM;AAAA,YACzB,GAAG,SAAU,OAAO;AAChB,uBAAS,KAAK;AAAA,YAClB,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,iBAAS,oBAAoB,SAAS,UAAU,eAAe;AAC3D,cAAI,OAAO,aAAa,YAAY;AAChC,oBAAQ,KAAK,QAAQ;AAAA,UACzB;AAEA,cAAI,OAAO,kBAAkB,YAAY;AACrC,oBAAQ,OAAO,EAAE,aAAa;AAAA,UAClC;AAAA,QACJ;AAEA,iBAAS,aAAaQ,MAAK;AAEvB,cAAI,OAAOA,SAAQ,UAAU;AACzB,oBAAQ,KAAKA,OAAM,yCAAyC;AAC5D,YAAAA,OAAM,OAAOA,IAAG;AAAA,UACpB;AAEA,iBAAOA;AAAA,QACX;AAEA,iBAAS,cAAc;AACnB,cAAI,UAAU,UAAU,OAAO,UAAU,UAAU,SAAS,CAAC,MAAM,YAAY;AAC3E,mBAAO,UAAU,UAAU,SAAS,CAAC;AAAA,UACzC;AAAA,QACJ;AAKA,YAAI,4BAA4B;AAChC,YAAI,gBAAgB;AACpB,YAAI,aAAa,CAAC;AAClB,YAAI,WAAW,OAAO,UAAU;AAGhC,YAAI,YAAY;AAChB,YAAI,aAAa;AAOjB,iBAAS,wBAAwB,KAAK;AAClC,cAAIC,UAAS,IAAI;AACjB,cAAI,MAAM,IAAI,YAAYA,OAAM;AAChC,cAAI,MAAM,IAAI,WAAW,GAAG;AAC5B,mBAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC7B,gBAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,UAC7B;AACA,iBAAO;AAAA,QACX;AAiBA,iBAAS,gCAAgCC,MAAK;AAC1C,iBAAO,IAAI,UAAU,SAAU,SAAS;AACpC,gBAAI,MAAMA,KAAI,YAAY,2BAA2B,UAAU;AAC/D,gBAAI,OAAO,WAAW,CAAC,EAAE,CAAC;AAC1B,gBAAI,YAAY,yBAAyB,EAAE,IAAI,MAAM,KAAK;AAE1D,gBAAI,UAAU,SAAU,GAAG;AAGvB,gBAAE,eAAe;AACjB,gBAAE,gBAAgB;AAClB,sBAAQ,KAAK;AAAA,YACjB;AAEA,gBAAI,aAAa,WAAY;AACzB,kBAAI,gBAAgB,UAAU,UAAU,MAAM,eAAe;AAC7D,kBAAI,cAAc,UAAU,UAAU,MAAM,QAAQ;AAGpD,sBAAQ,eAAe,CAAC,iBAAiB,SAAS,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE;AAAA,YACjF;AAAA,UACJ,CAAC,EAAE,OAAO,EAAE,WAAY;AACpB,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AAEA,iBAAS,kBAAkBA,MAAK;AAC5B,cAAI,OAAO,kBAAkB,WAAW;AACpC,mBAAO,UAAU,QAAQ,aAAa;AAAA,UAC1C;AACA,iBAAO,gCAAgCA,IAAG,EAAE,KAAK,SAAU,OAAO;AAC9D,4BAAgB;AAChB,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AAEA,iBAAS,gBAAgB,QAAQ;AAC7B,cAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,cAAI,oBAAoB,CAAC;AAEzB,4BAAkB,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACjE,8BAAkB,UAAU;AAC5B,8BAAkB,SAAS;AAAA,UAC/B,CAAC;AAGD,oBAAU,mBAAmB,KAAK,iBAAiB;AAGnD,cAAI,CAAC,UAAU,SAAS;AACpB,sBAAU,UAAU,kBAAkB;AAAA,UAC1C,OAAO;AACH,sBAAU,UAAU,UAAU,QAAQ,KAAK,WAAY;AACnD,qBAAO,kBAAkB;AAAA,YAC7B,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,iBAAS,kBAAkB,QAAQ;AAC/B,cAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,cAAI,oBAAoB,UAAU,mBAAmB,IAAI;AAIzD,cAAI,mBAAmB;AACnB,8BAAkB,QAAQ;AAC1B,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,QACJ;AAEA,iBAAS,iBAAiB,QAAQ,KAAK;AACnC,cAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,cAAI,oBAAoB,UAAU,mBAAmB,IAAI;AAIzD,cAAI,mBAAmB;AACnB,8BAAkB,OAAO,GAAG;AAC5B,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,QACJ;AAEA,iBAAS,eAAe,QAAQ,eAAe;AAC3C,iBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC5C,uBAAW,OAAO,IAAI,IAAI,WAAW,OAAO,IAAI,KAAK,gBAAgB;AAErE,gBAAI,OAAO,IAAI;AACX,kBAAI,eAAe;AACf,gCAAgB,MAAM;AACtB,uBAAO,GAAG,MAAM;AAAA,cACpB,OAAO;AACH,uBAAO,QAAQ,OAAO,EAAE;AAAA,cAC5B;AAAA,YACJ;AAEA,gBAAI,SAAS,CAAC,OAAO,IAAI;AAEzB,gBAAI,eAAe;AACf,qBAAO,KAAK,OAAO,OAAO;AAAA,YAC9B;AAEA,gBAAI,UAAU,IAAI,KAAK,MAAM,KAAK,MAAM;AAExC,gBAAI,eAAe;AACf,sBAAQ,kBAAkB,SAAU,GAAG;AACnC,oBAAI,KAAK,QAAQ;AACjB,oBAAI;AACA,qBAAG,kBAAkB,OAAO,SAAS;AACrC,sBAAI,EAAE,cAAc,GAAG;AAEnB,uBAAG,kBAAkB,yBAAyB;AAAA,kBAClD;AAAA,gBACJ,SAAS,IAAI;AACT,sBAAI,GAAG,SAAS,mBAAmB;AAC/B,4BAAQ,KAAK,mBAAmB,OAAO,OAAO,sCAA2C,EAAE,aAAa,iBAAiB,EAAE,aAAa,wBAAwB,OAAO,YAAY,mBAAmB;AAAA,kBAC1M,OAAO;AACH,0BAAM;AAAA,kBACV;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAEA,oBAAQ,UAAU,SAAU,GAAG;AAC3B,gBAAE,eAAe;AACjB,qBAAO,QAAQ,KAAK;AAAA,YACxB;AAEA,oBAAQ,YAAY,WAAY;AAC5B,kBAAI,KAAK,QAAQ;AACjB,iBAAG,kBAAkB,SAAU,GAAG;AAM9B,kBAAE,OAAO,MAAM;AAAA,cACnB;AACA,sBAAQ,EAAE;AACV,gCAAkB,MAAM;AAAA,YAC5B;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,iBAAS,uBAAuB,QAAQ;AACpC,iBAAO,eAAe,QAAQ,KAAK;AAAA,QACvC;AAEA,iBAAS,uBAAuB,QAAQ;AACpC,iBAAO,eAAe,QAAQ,IAAI;AAAA,QACtC;AAEA,iBAAS,iBAAiB,QAAQ,gBAAgB;AAC9C,cAAI,CAAC,OAAO,IAAI;AACZ,mBAAO;AAAA,UACX;AAEA,cAAI,aAAa,CAAC,OAAO,GAAG,iBAAiB,SAAS,OAAO,SAAS;AACtE,cAAI,cAAc,OAAO,UAAU,OAAO,GAAG;AAC7C,cAAI,YAAY,OAAO,UAAU,OAAO,GAAG;AAE3C,cAAI,aAAa;AAGb,gBAAI,OAAO,YAAY,gBAAgB;AACnC,sBAAQ,KAAK,mBAAmB,OAAO,OAAO,wCAA6C,OAAO,GAAG,UAAU,iBAAiB,OAAO,UAAU,GAAG;AAAA,YACxJ;AAEA,mBAAO,UAAU,OAAO,GAAG;AAAA,UAC/B;AAEA,cAAI,aAAa,YAAY;AAIzB,gBAAI,YAAY;AACZ,kBAAI,aAAa,OAAO,GAAG,UAAU;AACrC,kBAAI,aAAa,OAAO,SAAS;AAC7B,uBAAO,UAAU;AAAA,cACrB;AAAA,YACJ;AAEA,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AAGA,iBAAS,YAAY,MAAM;AACvB,iBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC5C,gBAAI,SAAS,IAAI,WAAW;AAC5B,mBAAO,UAAU;AACjB,mBAAO,YAAY,SAAU,GAAG;AAC5B,kBAAI,SAAS,KAAK,EAAE,OAAO,UAAU,EAAE;AACvC,sBAAQ;AAAA,gBACJ,6BAA6B;AAAA,gBAC7B,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,cACf,CAAC;AAAA,YACL;AACA,mBAAO,mBAAmB,IAAI;AAAA,UAClC,CAAC;AAAA,QACL;AAGA,iBAAS,YAAY,aAAa;AAC9B,cAAI,YAAY,wBAAwB,KAAK,YAAY,IAAI,CAAC;AAC9D,iBAAO,WAAW,CAAC,SAAS,GAAG,EAAE,MAAM,YAAY,KAAK,CAAC;AAAA,QAC7D;AAGA,iBAAS,eAAe,OAAO;AAC3B,iBAAO,SAAS,MAAM;AAAA,QAC1B;AAMA,iBAAS,YAAY,UAAU;AAC3B,cAAIJ,QAAO;AAEX,cAAI,UAAUA,MAAK,WAAW,EAAE,KAAK,WAAY;AAC7C,gBAAI,YAAY,WAAWA,MAAK,QAAQ,IAAI;AAE5C,gBAAI,aAAa,UAAU,SAAS;AAChC,qBAAO,UAAU;AAAA,YACrB;AAAA,UACJ,CAAC;AAED,8BAAoB,SAAS,UAAU,QAAQ;AAC/C,iBAAO;AAAA,QACX;AAKA,iBAAS,cAAc,QAAQ;AAC3B,0BAAgB,MAAM;AAEtB,cAAI,YAAY,WAAW,OAAO,IAAI;AACtC,cAAI,UAAU,UAAU;AAExB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,gBAAI,SAAS,QAAQ,CAAC;AACtB,gBAAI,OAAO,QAAQ,IAAI;AACnB,qBAAO,QAAQ,GAAG,MAAM;AACxB,qBAAO,QAAQ,KAAK;AAAA,YACxB;AAAA,UACJ;AACA,iBAAO,KAAK;AAEZ,iBAAO,uBAAuB,MAAM,EAAE,KAAK,SAAU,IAAI;AACrD,mBAAO,KAAK;AACZ,gBAAI,iBAAiB,MAAM,GAAG;AAE1B,qBAAO,uBAAuB,MAAM;AAAA,YACxC;AACA,mBAAO;AAAA,UACX,CAAC,EAAE,KAAK,SAAU,IAAI;AAGlB,mBAAO,KAAK,UAAU,KAAK;AAC3B,qBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,sBAAQA,EAAC,EAAE,QAAQ,KAAK;AAAA,YAC5B;AAAA,UACJ,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACvB,6BAAiB,QAAQ,GAAG;AAC5B,kBAAM;AAAA,UACV,CAAC;AAAA,QACL;AAIA,iBAAS,kBAAkB,QAAQ,MAAM,UAAU,SAAS;AACxD,cAAI,YAAY,QAAW;AACvB,sBAAU;AAAA,UACd;AAEA,cAAI;AACA,gBAAI,KAAK,OAAO,GAAG,YAAY,OAAO,WAAW,IAAI;AACrD,qBAAS,MAAM,EAAE;AAAA,UACrB,SAAS,KAAK;AACV,gBAAI,UAAU,MAAM,CAAC,OAAO,MAAM,IAAI,SAAS,uBAAuB,IAAI,SAAS,kBAAkB;AACjG,qBAAO,UAAU,QAAQ,EAAE,KAAK,WAAY;AACxC,oBAAI,CAAC,OAAO,MAAM,IAAI,SAAS,mBAAmB,CAAC,OAAO,GAAG,iBAAiB,SAAS,OAAO,SAAS,KAAK,OAAO,WAAW,OAAO,GAAG,SAAS;AAE7I,sBAAI,OAAO,IAAI;AACX,2BAAO,UAAU,OAAO,GAAG,UAAU;AAAA,kBACzC;AAEA,yBAAO,uBAAuB,MAAM;AAAA,gBACxC;AAAA,cACJ,CAAC,EAAE,KAAK,WAAY;AAChB,uBAAO,cAAc,MAAM,EAAE,KAAK,WAAY;AAC1C,oCAAkB,QAAQ,MAAM,UAAU,UAAU,CAAC;AAAA,gBACzD,CAAC;AAAA,cACL,CAAC,EAAE,OAAO,EAAE,QAAQ;AAAA,YACxB;AAEA,qBAAS,GAAG;AAAA,UAChB;AAAA,QACJ;AAEA,iBAAS,kBAAkB;AACvB,iBAAO;AAAA;AAAA,YAEH,SAAS,CAAC;AAAA;AAAA,YAEV,IAAI;AAAA;AAAA,YAEJ,SAAS;AAAA;AAAA,YAET,oBAAoB,CAAC;AAAA,UACzB;AAAA,QACJ;AAIA,iBAAS,aAAa,SAAS;AAC3B,cAAID,QAAO;AACX,cAAI,SAAS;AAAA,YACT,IAAI;AAAA,UACR;AAEA,cAAI,SAAS;AACT,qBAAS,KAAK,SAAS;AACnB,qBAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,YACzB;AAAA,UACJ;AAGA,cAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,cAAI,CAAC,WAAW;AACZ,wBAAY,gBAAgB;AAE5B,uBAAW,OAAO,IAAI,IAAI;AAAA,UAC9B;AAGA,oBAAU,QAAQ,KAAKA,KAAI;AAG3B,cAAI,CAACA,MAAK,YAAY;AAClB,YAAAA,MAAK,aAAaA,MAAK;AACvB,YAAAA,MAAK,QAAQ;AAAA,UACjB;AAGA,cAAI,eAAe,CAAC;AAEpB,mBAAS,eAAe;AAGpB,mBAAO,UAAU,QAAQ;AAAA,UAC7B;AAEA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,QAAQ,KAAK;AAC/C,gBAAI,SAAS,UAAU,QAAQ,CAAC;AAChC,gBAAI,WAAWA,OAAM;AAEjB,2BAAa,KAAK,OAAO,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;AAAA,YAChE;AAAA,UACJ;AAGA,cAAI,UAAU,UAAU,QAAQ,MAAM,CAAC;AAIvC,iBAAO,UAAU,IAAI,YAAY,EAAE,KAAK,WAAY;AAChD,mBAAO,KAAK,UAAU;AAEtB,mBAAO,uBAAuB,MAAM;AAAA,UACxC,CAAC,EAAE,KAAK,SAAU,IAAI;AAClB,mBAAO,KAAK;AACZ,gBAAI,iBAAiB,QAAQA,MAAK,eAAe,OAAO,GAAG;AAEvD,qBAAO,uBAAuB,MAAM;AAAA,YACxC;AACA,mBAAO;AAAA,UACX,CAAC,EAAE,KAAK,SAAU,IAAI;AAClB,mBAAO,KAAK,UAAU,KAAK;AAC3B,YAAAA,MAAK,UAAU;AAEf,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,kBAAIK,UAAS,QAAQ,CAAC;AACtB,kBAAIA,YAAWL,OAAM;AAEjB,gBAAAK,QAAO,QAAQ,KAAK,OAAO;AAC3B,gBAAAA,QAAO,QAAQ,UAAU,OAAO;AAAA,cACpC;AAAA,YACJ;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,iBAAS,QAAQH,MAAK,UAAU;AAC5B,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACnE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,MAAM,MAAM,IAAIE,IAAG;AAEvB,sBAAI,YAAY,WAAY;AACxB,wBAAI,QAAQ,IAAI;AAChB,wBAAI,UAAU,QAAW;AACrB,8BAAQ;AAAA,oBACZ;AACA,wBAAI,eAAe,KAAK,GAAG;AACvB,8BAAQ,YAAY,KAAK;AAAA,oBAC7B;AACA,4BAAQ,KAAK;AAAA,kBACjB;AAEA,sBAAI,UAAU,WAAY;AACtB,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAGA,iBAAS,QAAQ,UAAU,UAAU;AACjC,cAAIF,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACnE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,MAAM,MAAM,WAAW;AAC3B,sBAAI,kBAAkB;AAEtB,sBAAI,YAAY,WAAY;AACxB,wBAAI,SAAS,IAAI;AAEjB,wBAAI,QAAQ;AACR,0BAAI,QAAQ,OAAO;AACnB,0BAAI,eAAe,KAAK,GAAG;AACvB,gCAAQ,YAAY,KAAK;AAAA,sBAC7B;AACA,0BAAI,SAAS,SAAS,OAAO,OAAO,KAAK,iBAAiB;AAK1D,0BAAI,WAAW,QAAQ;AACnB,gCAAQ,MAAM;AAAA,sBAClB,OAAO;AACH,+BAAO,UAAU,EAAE;AAAA,sBACvB;AAAA,oBACJ,OAAO;AACH,8BAAQ;AAAA,oBACZ;AAAA,kBACJ;AAEA,sBAAI,UAAU,WAAY;AACtB,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AAEjC,iBAAO;AAAA,QACX;AAEA,iBAAS,QAAQE,MAAK,OAAO,UAAU;AACnC,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,gBAAI;AACJ,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,uBAASA,MAAK;AACd,kBAAI,SAAS,KAAK,KAAK,MAAM,iBAAiB;AAC1C,uBAAO,kBAAkB,OAAO,EAAE,EAAE,KAAK,SAAU,aAAa;AAC5D,sBAAI,aAAa;AACb,2BAAO;AAAA,kBACX;AACA,yBAAO,YAAY,KAAK;AAAA,gBAC5B,CAAC;AAAA,cACL;AACA,qBAAO;AAAA,YACX,CAAC,EAAE,KAAK,SAAUM,QAAO;AACrB,gCAAkBN,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACpE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAM1D,sBAAIM,WAAU,MAAM;AAChB,oBAAAA,SAAQ;AAAA,kBACZ;AAEA,sBAAI,MAAM,MAAM,IAAIA,QAAOJ,IAAG;AAE9B,8BAAY,aAAa,WAAY;AAOjC,wBAAII,WAAU,QAAW;AACrB,sBAAAA,SAAQ;AAAA,oBACZ;AAEA,4BAAQA,MAAK;AAAA,kBACjB;AACA,8BAAY,UAAU,YAAY,UAAU,WAAY;AACpD,wBAAIC,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,2BAAOA,IAAG;AAAA,kBACd;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,WAAWL,MAAK,UAAU;AAC/B,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACpE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAM1D,sBAAI,MAAM,MAAM,QAAQ,EAAEE,IAAG;AAC7B,8BAAY,aAAa,WAAY;AACjC,4BAAQ;AAAA,kBACZ;AAEA,8BAAY,UAAU,WAAY;AAC9B,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAIA,8BAAY,UAAU,WAAY;AAC9B,wBAAIK,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,2BAAOA,IAAG;AAAA,kBACd;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,MAAM,UAAU;AACrB,cAAIP,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACpE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,MAAM,MAAM,MAAM;AAEtB,8BAAY,aAAa,WAAY;AACjC,4BAAQ;AAAA,kBACZ;AAEA,8BAAY,UAAU,YAAY,UAAU,WAAY;AACpD,wBAAIO,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,2BAAOA,IAAG;AAAA,kBACd;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,OAAO,UAAU;AACtB,cAAIP,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACnE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,MAAM,MAAM,MAAM;AAEtB,sBAAI,YAAY,WAAY;AACxB,4BAAQ,IAAI,MAAM;AAAA,kBACtB;AAEA,sBAAI,UAAU,WAAY;AACtB,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,IAAI,GAAG,UAAU;AACtB,cAAIA,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,gBAAI,IAAI,GAAG;AACP,sBAAQ,IAAI;AAEZ;AAAA,YACJ;AAEA,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACnE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,WAAW;AACf,sBAAI,MAAM,MAAM,cAAc;AAE9B,sBAAI,YAAY,WAAY;AACxB,wBAAI,SAAS,IAAI;AACjB,wBAAI,CAAC,QAAQ;AAET,8BAAQ,IAAI;AAEZ;AAAA,oBACJ;AAEA,wBAAI,MAAM,GAAG;AAGT,8BAAQ,OAAO,GAAG;AAAA,oBACtB,OAAO;AACH,0BAAI,CAAC,UAAU;AAGX,mCAAW;AACX,+BAAO,QAAQ,CAAC;AAAA,sBACpB,OAAO;AAEH,gCAAQ,OAAO,GAAG;AAAA,sBACtB;AAAA,oBACJ;AAAA,kBACJ;AAEA,sBAAI,UAAU,WAAY;AACtB,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,KAAK,UAAU;AACpB,cAAIA,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,gCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACnE,oBAAI,KAAK;AACL,yBAAO,OAAO,GAAG;AAAA,gBACrB;AAEA,oBAAI;AACA,sBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,sBAAI,MAAM,MAAM,cAAc;AAC9B,sBAAIQ,QAAO,CAAC;AAEZ,sBAAI,YAAY,WAAY;AACxB,wBAAI,SAAS,IAAI;AAEjB,wBAAI,CAAC,QAAQ;AACT,8BAAQA,KAAI;AACZ;AAAA,oBACJ;AAEA,oBAAAA,MAAK,KAAK,OAAO,GAAG;AACpB,2BAAO,UAAU,EAAE;AAAA,kBACvB;AAEA,sBAAI,UAAU,WAAY;AACtB,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAAA,gBACJ,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,aAAa,SAAS,UAAU;AACrC,qBAAW,YAAY,MAAM,MAAM,SAAS;AAE5C,cAAI,gBAAgB,KAAK,OAAO;AAChC,oBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,cAAI,CAAC,QAAQ,MAAM;AACf,oBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,oBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,UAC3D;AAEA,cAAIR,QAAO;AACX,cAAI;AACJ,cAAI,CAAC,QAAQ,MAAM;AACf,sBAAU,UAAU,OAAO,mBAAmB;AAAA,UAClD,OAAO;AACH,gBAAI,cAAc,QAAQ,SAAS,cAAc,QAAQA,MAAK,QAAQ;AAEtE,gBAAI,YAAY,cAAc,UAAU,QAAQA,MAAK,QAAQ,EAAE,IAAI,uBAAuB,OAAO,EAAE,KAAK,SAAU,IAAI;AAClH,kBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,kBAAI,UAAU,UAAU;AACxB,wBAAU,KAAK;AACf,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,cAC5B;AACA,qBAAO;AAAA,YACX,CAAC;AAED,gBAAI,CAAC,QAAQ,WAAW;AACpB,wBAAU,UAAU,KAAK,SAAU,IAAI;AACnC,gCAAgB,OAAO;AAEvB,oBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,oBAAI,UAAU,UAAU;AAExB,mBAAG,MAAM;AACT,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAI,SAAS,QAAQ,CAAC;AACtB,yBAAO,QAAQ,KAAK;AAAA,gBACxB;AAEA,oBAAI,gBAAgB,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,sBAAI,MAAM,IAAI,eAAe,QAAQ,IAAI;AAEzC,sBAAI,UAAU,WAAY;AACtB,wBAAIS,MAAK,IAAI;AACb,wBAAIA,KAAI;AACJ,sBAAAA,IAAG,MAAM;AAAA,oBACb;AACA,2BAAO,IAAI,KAAK;AAAA,kBACpB;AAEA,sBAAI,YAAY,WAAY;AAGxB,4BAAQ,KAAK,wCAAwC,QAAQ,OAAO,yCAAyC;AAAA,kBACjH;AAEA,sBAAI,YAAY,WAAY;AACxB,wBAAIA,MAAK,IAAI;AACb,wBAAIA,KAAI;AACJ,sBAAAA,IAAG,MAAM;AAAA,oBACb;AACA,4BAAQA,GAAE;AAAA,kBACd;AAAA,gBACJ,CAAC;AAED,uBAAO,cAAc,KAAK,SAAUA,KAAI;AACpC,4BAAU,KAAKA;AACf,2BAASR,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,wBAAI,UAAU,QAAQA,EAAC;AACvB,sCAAkB,QAAQ,OAAO;AAAA,kBACrC;AAAA,gBACJ,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACvB,mBAAC,iBAAiB,SAAS,GAAG,KAAK,UAAU,QAAQ,GAAG,OAAO,EAAE,WAAY;AAAA,kBAAC,CAAC;AAC/E,wBAAM;AAAA,gBACV,CAAC;AAAA,cACL,CAAC;AAAA,YACL,OAAO;AACH,wBAAU,UAAU,KAAK,SAAU,IAAI;AACnC,oBAAI,CAAC,GAAG,iBAAiB,SAAS,QAAQ,SAAS,GAAG;AAClD;AAAA,gBACJ;AAEA,oBAAI,aAAa,GAAG,UAAU;AAE9B,gCAAgB,OAAO;AAEvB,oBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,oBAAI,UAAU,UAAU;AAExB,mBAAG,MAAM;AACT,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAI,SAAS,QAAQ,CAAC;AACtB,yBAAO,QAAQ,KAAK;AACpB,yBAAO,QAAQ,UAAU;AAAA,gBAC7B;AAEA,oBAAI,oBAAoB,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC7D,sBAAI,MAAM,IAAI,KAAK,QAAQ,MAAM,UAAU;AAE3C,sBAAI,UAAU,SAAU,KAAK;AACzB,wBAAIQ,MAAK,IAAI;AACb,oBAAAA,IAAG,MAAM;AACT,2BAAO,GAAG;AAAA,kBACd;AAEA,sBAAI,kBAAkB,WAAY;AAC9B,wBAAIA,MAAK,IAAI;AACb,oBAAAA,IAAG,kBAAkB,QAAQ,SAAS;AAAA,kBAC1C;AAEA,sBAAI,YAAY,WAAY;AACxB,wBAAIA,MAAK,IAAI;AACb,oBAAAA,IAAG,MAAM;AACT,4BAAQA,GAAE;AAAA,kBACd;AAAA,gBACJ,CAAC;AAED,uBAAO,kBAAkB,KAAK,SAAUA,KAAI;AACxC,4BAAU,KAAKA;AACf,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,WAAW,QAAQ,CAAC;AACxB,6BAAS,QAAQ,KAAKA;AACtB,sCAAkB,SAAS,OAAO;AAAA,kBACtC;AAAA,gBACJ,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACvB,mBAAC,iBAAiB,SAAS,GAAG,KAAK,UAAU,QAAQ,GAAG,OAAO,EAAE,WAAY;AAAA,kBAAC,CAAC;AAC/E,wBAAM;AAAA,gBACV,CAAC;AAAA,cACL,CAAC;AAAA,YACL;AAAA,UACJ;AAEA,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,YAAI,eAAe;AAAA,UACf,SAAS;AAAA,UACT;AAAA,UACA,UAAU,iBAAiB;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAEA,iBAAS,gBAAgB;AACrB,iBAAO,OAAO,iBAAiB;AAAA,QACnC;AAKA,YAAI,aAAa;AAEjB,YAAI,mBAAmB;AACvB,YAAI,yBAAyB;AAE7B,YAAI,oBAAoB;AACxB,YAAI,2BAA2B,kBAAkB;AAGjD,YAAI,mBAAmB;AACvB,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,yBAAyB;AAC7B,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,mBAAmB;AACvB,YAAI,mBAAmB;AACvB,YAAI,oBAAoB;AACxB,YAAI,oBAAoB;AACxB,YAAI,gCAAgC,2BAA2B,iBAAiB;AAEhF,YAAI,aAAa,OAAO,UAAU;AAElC,iBAAS,eAAe,kBAAkB;AAEtC,cAAI,eAAe,iBAAiB,SAAS;AAC7C,cAAI,MAAM,iBAAiB;AAC3B,cAAI;AACJ,cAAI,IAAI;AACR,cAAI,UAAU,UAAU,UAAU;AAElC,cAAI,iBAAiB,iBAAiB,SAAS,CAAC,MAAM,KAAK;AACvD;AACA,gBAAI,iBAAiB,iBAAiB,SAAS,CAAC,MAAM,KAAK;AACvD;AAAA,YACJ;AAAA,UACJ;AAEA,cAAI,SAAS,IAAI,YAAY,YAAY;AACzC,cAAI,QAAQ,IAAI,WAAW,MAAM;AAEjC,eAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AACzB,uBAAW,WAAW,QAAQ,iBAAiB,CAAC,CAAC;AACjD,uBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AACrD,uBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AACrD,uBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AAGrD,kBAAM,GAAG,IAAI,YAAY,IAAI,YAAY;AACzC,kBAAM,GAAG,KAAK,WAAW,OAAO,IAAI,YAAY;AAChD,kBAAM,GAAG,KAAK,WAAW,MAAM,IAAI,WAAW;AAAA,UAClD;AACA,iBAAO;AAAA,QACX;AAIA,iBAAS,eAAe,QAAQ;AAE5B,cAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,cAAI,eAAe;AACnB,cAAI;AAEJ,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AAElC,4BAAgB,WAAW,MAAM,CAAC,KAAK,CAAC;AACxC,4BAAgB,YAAY,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC;AAClE,4BAAgB,YAAY,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC;AACvE,4BAAgB,WAAW,MAAM,IAAI,CAAC,IAAI,EAAE;AAAA,UAChD;AAEA,cAAI,MAAM,SAAS,MAAM,GAAG;AACxB,2BAAe,aAAa,UAAU,GAAG,aAAa,SAAS,CAAC,IAAI;AAAA,UACxE,WAAW,MAAM,SAAS,MAAM,GAAG;AAC/B,2BAAe,aAAa,UAAU,GAAG,aAAa,SAAS,CAAC,IAAI;AAAA,UACxE;AAEA,iBAAO;AAAA,QACX;AAKA,iBAAS,UAAU,OAAO,UAAU;AAChC,cAAI,YAAY;AAChB,cAAI,OAAO;AACP,wBAAY,WAAW,KAAK,KAAK;AAAA,UACrC;AAMA,cAAI,UAAU,cAAc,0BAA0B,MAAM,UAAU,WAAW,KAAK,MAAM,MAAM,MAAM,yBAAyB;AAG7H,gBAAI;AACJ,gBAAI,SAAS;AAEb,gBAAI,iBAAiB,aAAa;AAC9B,uBAAS;AACT,wBAAU;AAAA,YACd,OAAO;AACH,uBAAS,MAAM;AAEf,kBAAI,cAAc,sBAAsB;AACpC,0BAAU;AAAA,cACd,WAAW,cAAc,uBAAuB;AAC5C,0BAAU;AAAA,cACd,WAAW,cAAc,8BAA8B;AACnD,0BAAU;AAAA,cACd,WAAW,cAAc,uBAAuB;AAC5C,0BAAU;AAAA,cACd,WAAW,cAAc,wBAAwB;AAC7C,0BAAU;AAAA,cACd,WAAW,cAAc,uBAAuB;AAC5C,0BAAU;AAAA,cACd,WAAW,cAAc,wBAAwB;AAC7C,0BAAU;AAAA,cACd,WAAW,cAAc,yBAAyB;AAC9C,0BAAU;AAAA,cACd,WAAW,cAAc,yBAAyB;AAC9C,0BAAU;AAAA,cACd,OAAO;AACH,yBAAS,IAAI,MAAM,oCAAoC,CAAC;AAAA,cAC5D;AAAA,YACJ;AAEA,qBAAS,SAAS,eAAe,MAAM,CAAC;AAAA,UAC5C,WAAW,cAAc,iBAAiB;AAEtC,gBAAI,aAAa,IAAI,WAAW;AAEhC,uBAAW,SAAS,WAAY;AAE5B,kBAAI,MAAM,mBAAmB,MAAM,OAAO,MAAM,eAAe,KAAK,MAAM;AAE1E,uBAAS,oBAAoB,YAAY,GAAG;AAAA,YAChD;AAEA,uBAAW,kBAAkB,KAAK;AAAA,UACtC,OAAO;AACH,gBAAI;AACA,uBAAS,KAAK,UAAU,KAAK,CAAC;AAAA,YAClC,SAAS,GAAG;AACR,sBAAQ,MAAM,+CAA+C,KAAK;AAElE,uBAAS,MAAM,CAAC;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAUA,iBAAS,YAAY,OAAO;AAIxB,cAAI,MAAM,UAAU,GAAG,wBAAwB,MAAM,mBAAmB;AACpE,mBAAO,KAAK,MAAM,KAAK;AAAA,UAC3B;AAKA,cAAI,mBAAmB,MAAM,UAAU,6BAA6B;AACpE,cAAI,OAAO,MAAM,UAAU,0BAA0B,6BAA6B;AAElF,cAAI;AAGJ,cAAI,SAAS,aAAa,uBAAuB,KAAK,gBAAgB,GAAG;AACrE,gBAAI,UAAU,iBAAiB,MAAM,sBAAsB;AAC3D,uBAAW,QAAQ,CAAC;AACpB,+BAAmB,iBAAiB,UAAU,QAAQ,CAAC,EAAE,MAAM;AAAA,UACnE;AACA,cAAI,SAAS,eAAe,gBAAgB;AAI5C,kBAAQ,MAAM;AAAA,YACV,KAAK;AACD,qBAAO;AAAA,YACX,KAAK;AACD,qBAAO,WAAW,CAAC,MAAM,GAAG,EAAE,MAAM,SAAS,CAAC;AAAA,YAClD,KAAK;AACD,qBAAO,IAAI,UAAU,MAAM;AAAA,YAC/B,KAAK;AACD,qBAAO,IAAI,WAAW,MAAM;AAAA,YAChC,KAAK;AACD,qBAAO,IAAI,kBAAkB,MAAM;AAAA,YACvC,KAAK;AACD,qBAAO,IAAI,WAAW,MAAM;AAAA,YAChC,KAAK;AACD,qBAAO,IAAI,YAAY,MAAM;AAAA,YACjC,KAAK;AACD,qBAAO,IAAI,WAAW,MAAM;AAAA,YAChC,KAAK;AACD,qBAAO,IAAI,YAAY,MAAM;AAAA,YACjC,KAAK;AACD,qBAAO,IAAI,aAAa,MAAM;AAAA,YAClC,KAAK;AACD,qBAAO,IAAI,aAAa,MAAM;AAAA,YAClC;AACI,oBAAM,IAAI,MAAM,kBAAkB,IAAI;AAAA,UAC9C;AAAA,QACJ;AAEA,YAAI,wBAAwB;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAYA,iBAAS,cAAc,GAAG,QAAQ,UAAU,eAAe;AACvD,YAAE,WAAW,gCAAgC,OAAO,YAAY,gDAAqD,CAAC,GAAG,UAAU,aAAa;AAAA,QACpJ;AAIA,iBAAS,eAAe,SAAS;AAC7B,cAAIT,QAAO;AACX,cAAI,SAAS;AAAA,YACT,IAAI;AAAA,UACR;AAEA,cAAI,SAAS;AACT,qBAAS,KAAK,SAAS;AACnB,qBAAO,CAAC,IAAI,OAAO,QAAQ,CAAC,MAAM,WAAW,QAAQ,CAAC,EAAE,SAAS,IAAI,QAAQ,CAAC;AAAA,YAClF;AAAA,UACJ;AAEA,cAAI,gBAAgB,IAAI,UAAU,SAAU,SAAS,QAAQ;AAGzD,gBAAI;AACA,qBAAO,KAAK,aAAa,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,aAAa,OAAO,IAAI;AAAA,YACjG,SAAS,GAAG;AACR,qBAAO,OAAO,CAAC;AAAA,YACnB;AAGA,mBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,4BAAc,GAAG,QAAQ,WAAY;AACjC,gBAAAA,MAAK,UAAU;AACf,wBAAQ;AAAA,cACZ,GAAG,SAAUU,IAAG,OAAO;AACnB,uBAAO,KAAK;AAAA,cAChB,CAAC;AAAA,YACL,GAAG,MAAM;AAAA,UACb,CAAC;AAED,iBAAO,aAAa;AACpB,iBAAO;AAAA,QACX;AAEA,iBAAS,cAAc,GAAG,QAAQ,cAAc,MAAM,UAAU,eAAe;AAC3E,YAAE,WAAW,cAAc,MAAM,UAAU,SAAUA,IAAG,OAAO;AAC3D,gBAAI,MAAM,SAAS,MAAM,YAAY;AACjC,cAAAA,GAAE,WAAW,kEAAuE,CAAC,OAAO,SAAS,GAAG,SAAUA,IAAG,SAAS;AAC1H,oBAAI,CAAC,QAAQ,KAAK,QAAQ;AAGtB,gCAAcA,IAAG,QAAQ,WAAY;AACjC,oBAAAA,GAAE,WAAW,cAAc,MAAM,UAAU,aAAa;AAAA,kBAC5D,GAAG,aAAa;AAAA,gBACpB,OAAO;AACH,gCAAcA,IAAG,KAAK;AAAA,gBAC1B;AAAA,cACJ,GAAG,aAAa;AAAA,YACpB,OAAO;AACH,4BAAcA,IAAG,KAAK;AAAA,YAC1B;AAAA,UACJ,GAAG,aAAa;AAAA,QACpB;AAEA,iBAAS,UAAUR,MAAK,UAAU;AAC9B,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,mBAAmB,OAAO,YAAY,0BAA0B,CAACE,IAAG,GAAG,SAAUQ,IAAG,SAAS;AAClH,sBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE,QAAQ;AAIhE,sBAAI,QAAQ;AACR,6BAAS,OAAO,WAAW,YAAY,MAAM;AAAA,kBACjD;AAEA,0BAAQ,MAAM;AAAA,gBAClB,GAAG,SAAUA,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,UAAU,UAAU,UAAU;AACnC,cAAIV,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAElB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,mBAAmB,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AACpF,sBAAI,OAAO,QAAQ;AACnB,sBAAIP,UAAS,KAAK;AAElB,2BAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC7B,wBAAI,OAAO,KAAK,KAAK,CAAC;AACtB,wBAAI,SAAS,KAAK;AAIlB,wBAAI,QAAQ;AACR,+BAAS,OAAO,WAAW,YAAY,MAAM;AAAA,oBACjD;AAEA,6BAAS,SAAS,QAAQ,KAAK,KAAK,IAAI,CAAC;AAIzC,wBAAI,WAAW,QAAQ;AACnB,8BAAQ,MAAM;AACd;AAAA,oBACJ;AAAA,kBACJ;AAEA,0BAAQ;AAAA,gBACZ,GAAG,SAAUO,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,SAASR,MAAK,OAAO,UAAU,aAAa;AACjD,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAI1B,kBAAI,UAAU,QAAW;AACrB,wBAAQ;AAAA,cACZ;AAGA,kBAAI,gBAAgB;AAEpB,kBAAI,SAASA,MAAK;AAClB,qBAAO,WAAW,UAAU,OAAO,SAAUM,QAAO,OAAO;AACvD,oBAAI,OAAO;AACP,yBAAO,KAAK;AAAA,gBAChB,OAAO;AACH,yBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,kCAAc,GAAG,QAAQ,4BAA4B,OAAO,YAAY,+BAAoC,CAACJ,MAAKI,MAAK,GAAG,WAAY;AAClI,8BAAQ,aAAa;AAAA,oBACzB,GAAG,SAAUI,IAAGC,QAAO;AACnB,6BAAOA,MAAK;AAAA,oBAChB,CAAC;AAAA,kBACL,GAAG,SAAU,UAAU;AAGnB,wBAAI,SAAS,SAAS,SAAS,WAAW;AAQtC,0BAAI,cAAc,GAAG;AACjB,gCAAQ,SAAS,MAAMX,OAAM,CAACE,MAAK,eAAe,UAAU,cAAc,CAAC,CAAC,CAAC;AAC7E;AAAA,sBACJ;AACA,6BAAO,QAAQ;AAAA,oBACnB;AAAA,kBACJ,CAAC;AAAA,gBACL;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,UAAUA,MAAK,OAAO,UAAU;AACrC,iBAAO,SAAS,MAAM,MAAM,CAACA,MAAK,OAAO,UAAU,CAAC,CAAC;AAAA,QACzD;AAEA,iBAAS,aAAaA,MAAK,UAAU;AACjC,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,iBAAiB,OAAO,YAAY,kBAAkB,CAACE,IAAG,GAAG,WAAY;AAC9F,0BAAQ;AAAA,gBACZ,GAAG,SAAUQ,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAIA,iBAAS,QAAQ,UAAU;AACvB,cAAIV,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,iBAAiB,OAAO,WAAW,CAAC,GAAG,WAAY;AACxE,0BAAQ;AAAA,gBACZ,GAAG,SAAUU,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAIA,iBAAS,SAAS,UAAU;AACxB,cAAIV,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAE/B,8BAAc,GAAG,QAAQ,iCAAiC,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AAClG,sBAAI,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE;AAClC,0BAAQ,MAAM;AAAA,gBAClB,GAAG,SAAUA,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AASA,iBAAS,MAAM,GAAG,UAAU;AACxB,cAAIV,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,qBAAqB,OAAO,YAAY,yBAAyB,CAAC,IAAI,CAAC,GAAG,SAAUU,IAAG,SAAS;AACrH,sBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE,MAAM;AAC9D,0BAAQ,MAAM;AAAA,gBAClB,GAAG,SAAUA,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,OAAO,UAAU;AACtB,cAAIV,QAAO;AAEX,cAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,YAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1B,kBAAI,SAASA,MAAK;AAClB,qBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,8BAAc,GAAG,QAAQ,qBAAqB,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AACtF,sBAAIF,QAAO,CAAC;AAEZ,2BAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC1C,oBAAAA,MAAK,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,GAAG;AAAA,kBACtC;AAEA,0BAAQA,KAAI;AAAA,gBAChB,GAAG,SAAUE,IAAG,OAAO;AACnB,yBAAO,KAAK;AAAA,gBAChB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,UACtB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAIA,iBAAS,iBAAiB,IAAI;AAC1B,iBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC5C,eAAG,YAAY,SAAU,GAAG;AACxB,gBAAE,WAAW,+FAAoG,CAAC,GAAG,SAAUA,IAAG,SAAS;AACvI,oBAAI,aAAa,CAAC;AAElB,yBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC1C,6BAAW,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,IAAI;AAAA,gBAC7C;AAEA,wBAAQ;AAAA,kBACJ;AAAA,kBACA;AAAA,gBACJ,CAAC;AAAA,cACL,GAAG,SAAUA,IAAG,OAAO;AACnB,uBAAO,KAAK;AAAA,cAChB,CAAC;AAAA,YACL,GAAG,SAAU,UAAU;AACnB,qBAAO,QAAQ;AAAA,YACnB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAEA,iBAAS,eAAe,SAAS,UAAU;AACvC,qBAAW,YAAY,MAAM,MAAM,SAAS;AAE5C,cAAI,gBAAgB,KAAK,OAAO;AAChC,oBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,cAAI,CAAC,QAAQ,MAAM;AACf,oBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,oBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,UAC3D;AAEA,cAAIV,QAAO;AACX,cAAI;AACJ,cAAI,CAAC,QAAQ,MAAM;AACf,sBAAU,UAAU,OAAO,mBAAmB;AAAA,UAClD,OAAO;AACH,sBAAU,IAAI,UAAU,SAAU,SAAS;AACvC,kBAAI;AACJ,kBAAI,QAAQ,SAAS,cAAc,MAAM;AAErC,qBAAKA,MAAK,QAAQ;AAAA,cACtB,OAAO;AACH,qBAAK,aAAa,QAAQ,MAAM,IAAI,IAAI,CAAC;AAAA,cAC7C;AAEA,kBAAI,CAAC,QAAQ,WAAW;AAEpB,wBAAQ,iBAAiB,EAAE,CAAC;AAAA,cAChC,OAAO;AACH,wBAAQ;AAAA,kBACJ;AAAA,kBACA,YAAY,CAAC,QAAQ,SAAS;AAAA,gBAClC,CAAC;AAAA,cACL;AAAA,YACJ,CAAC,EAAE,KAAK,SAAU,eAAe;AAC7B,qBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC5C,8BAAc,GAAG,YAAY,SAAU,GAAG;AACtC,2BAAS,UAAU,WAAW;AAC1B,2BAAO,IAAI,UAAU,SAAUY,UAASC,SAAQ;AAC5C,wBAAE,WAAW,0BAA0B,WAAW,CAAC,GAAG,WAAY;AAC9D,wBAAAD,SAAQ;AAAA,sBACZ,GAAG,SAAUF,IAAG,OAAO;AACnB,wBAAAG,QAAO,KAAK;AAAA,sBAChB,CAAC;AAAA,oBACL,CAAC;AAAA,kBACL;AAEA,sBAAI,aAAa,CAAC;AAClB,2BAAS,IAAI,GAAG,MAAM,cAAc,WAAW,QAAQ,IAAI,KAAK,KAAK;AACjE,+BAAW,KAAK,UAAU,cAAc,WAAW,CAAC,CAAC,CAAC;AAAA,kBAC1D;AAEA,4BAAU,IAAI,UAAU,EAAE,KAAK,WAAY;AACvC,4BAAQ;AAAA,kBACZ,CAAC,EAAE,OAAO,EAAE,SAAU,GAAG;AACrB,2BAAO,CAAC;AAAA,kBACZ,CAAC;AAAA,gBACL,GAAG,SAAU,UAAU;AACnB,yBAAO,QAAQ;AAAA,gBACnB,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAEA,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,YAAI,gBAAgB;AAAA,UAChB,SAAS;AAAA,UACT,cAAc;AAAA,UACd,UAAU,cAAc;AAAA,UACxB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAEA,iBAAS,sBAAsB;AAC3B,cAAI;AACA,mBAAO,OAAO,iBAAiB,eAAe,aAAa;AAAA,YAE3D,CAAC,CAAC,aAAa;AAAA,UACnB,SAAS,GAAG;AACR,mBAAO;AAAA,UACX;AAAA,QACJ;AAEA,iBAAS,cAAc,SAAS,eAAe;AAC3C,cAAI,YAAY,QAAQ,OAAO;AAE/B,cAAI,QAAQ,cAAc,cAAc,WAAW;AAC/C,yBAAa,QAAQ,YAAY;AAAA,UACrC;AACA,iBAAO;AAAA,QACX;AAGA,iBAAS,4BAA4B;AACjC,cAAI,sBAAsB;AAE1B,cAAI;AACA,yBAAa,QAAQ,qBAAqB,IAAI;AAC9C,yBAAa,WAAW,mBAAmB;AAE3C,mBAAO;AAAA,UACX,SAAS,GAAG;AACR,mBAAO;AAAA,UACX;AAAA,QACJ;AAMA,iBAAS,wBAAwB;AAC7B,iBAAO,CAAC,0BAA0B,KAAK,aAAa,SAAS;AAAA,QACjE;AAGA,iBAAS,eAAe,SAAS;AAC7B,cAAIb,QAAO;AACX,cAAI,SAAS,CAAC;AACd,cAAI,SAAS;AACT,qBAAS,KAAK,SAAS;AACnB,qBAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,YACzB;AAAA,UACJ;AAEA,iBAAO,YAAY,cAAc,SAASA,MAAK,cAAc;AAE7D,cAAI,CAAC,sBAAsB,GAAG;AAC1B,mBAAO,UAAU,OAAO;AAAA,UAC5B;AAEA,UAAAA,MAAK,UAAU;AACf,iBAAO,aAAa;AAEpB,iBAAO,UAAU,QAAQ;AAAA,QAC7B;AAIA,iBAAS,QAAQ,UAAU;AACvB,cAAIA,QAAO;AACX,cAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,YAAYA,MAAK,QAAQ;AAE7B,qBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,kBAAIE,OAAM,aAAa,IAAI,CAAC;AAE5B,kBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAC9B,6BAAa,WAAWA,IAAG;AAAA,cAC/B;AAAA,YACJ;AAAA,UACJ,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAKA,iBAAS,UAAUA,MAAK,UAAU;AAC9B,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,SAASA,MAAK;AAClB,gBAAI,SAAS,aAAa,QAAQ,OAAO,YAAYE,IAAG;AAMxD,gBAAI,QAAQ;AACR,uBAAS,OAAO,WAAW,YAAY,MAAM;AAAA,YACjD;AAEA,mBAAO;AAAA,UACX,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAGA,iBAAS,UAAU,UAAU,UAAU;AACnC,cAAIF,QAAO;AAEX,cAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,SAASA,MAAK;AAClB,gBAAI,YAAY,OAAO;AACvB,gBAAI,kBAAkB,UAAU;AAChC,gBAAIG,UAAS,aAAa;AAQ1B,gBAAI,kBAAkB;AAEtB,qBAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC7B,kBAAID,OAAM,aAAa,IAAI,CAAC;AAC5B,kBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAC9B;AAAA,cACJ;AACA,kBAAI,QAAQ,aAAa,QAAQA,IAAG;AAMpC,kBAAI,OAAO;AACP,wBAAQ,OAAO,WAAW,YAAY,KAAK;AAAA,cAC/C;AAEA,sBAAQ,SAAS,OAAOA,KAAI,UAAU,eAAe,GAAG,iBAAiB;AAEzE,kBAAI,UAAU,QAAQ;AAClB,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAGA,iBAAS,MAAM,GAAG,UAAU;AACxB,cAAIF,QAAO;AACX,cAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,SAASA,MAAK;AAClB,gBAAI;AACJ,gBAAI;AACA,uBAAS,aAAa,IAAI,CAAC;AAAA,YAC/B,SAAS,OAAO;AACZ,uBAAS;AAAA,YACb;AAGA,gBAAI,QAAQ;AACR,uBAAS,OAAO,UAAU,OAAO,UAAU,MAAM;AAAA,YACrD;AAEA,mBAAO;AAAA,UACX,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,OAAO,UAAU;AACtB,cAAIA,QAAO;AACX,cAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,SAASA,MAAK;AAClB,gBAAIG,UAAS,aAAa;AAC1B,gBAAIK,QAAO,CAAC;AAEZ,qBAAS,IAAI,GAAG,IAAIL,SAAQ,KAAK;AAC7B,kBAAI,UAAU,aAAa,IAAI,CAAC;AAChC,kBAAI,QAAQ,QAAQ,OAAO,SAAS,MAAM,GAAG;AACzC,gBAAAK,MAAK,KAAK,QAAQ,UAAU,OAAO,UAAU,MAAM,CAAC;AAAA,cACxD;AAAA,YACJ;AAEA,mBAAOA;AAAA,UACX,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAGA,iBAAS,SAAS,UAAU;AACxB,cAAIR,QAAO;AACX,cAAI,UAAUA,MAAK,KAAK,EAAE,KAAK,SAAUQ,OAAM;AAC3C,mBAAOA,MAAK;AAAA,UAChB,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAGA,iBAAS,aAAaN,MAAK,UAAU;AACjC,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AACxC,gBAAI,SAASA,MAAK;AAClB,yBAAa,WAAW,OAAO,YAAYE,IAAG;AAAA,UAClD,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAMA,iBAAS,UAAUA,MAAK,OAAO,UAAU;AACrC,cAAIF,QAAO;AAEX,UAAAE,OAAM,aAAaA,IAAG;AAEtB,cAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AAGxC,gBAAI,UAAU,QAAW;AACrB,sBAAQ;AAAA,YACZ;AAGA,gBAAI,gBAAgB;AAEpB,mBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC5C,kBAAI,SAASA,MAAK;AAClB,qBAAO,WAAW,UAAU,OAAO,SAAUM,QAAO,OAAO;AACvD,oBAAI,OAAO;AACP,yBAAO,KAAK;AAAA,gBAChB,OAAO;AACH,sBAAI;AACA,iCAAa,QAAQ,OAAO,YAAYJ,MAAKI,MAAK;AAClD,4BAAQ,aAAa;AAAA,kBACzB,SAAS,GAAG;AAGR,wBAAI,EAAE,SAAS,wBAAwB,EAAE,SAAS,8BAA8B;AAC5E,6BAAO,CAAC;AAAA,oBACZ;AACA,2BAAO,CAAC;AAAA,kBACZ;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL,CAAC;AAED,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,iBAAS,eAAe,SAAS,UAAU;AACvC,qBAAW,YAAY,MAAM,MAAM,SAAS;AAE5C,oBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,cAAI,CAAC,QAAQ,MAAM;AACf,gBAAI,gBAAgB,KAAK,OAAO;AAChC,oBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,oBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,UAC3D;AAEA,cAAIN,QAAO;AACX,cAAI;AACJ,cAAI,CAAC,QAAQ,MAAM;AACf,sBAAU,UAAU,OAAO,mBAAmB;AAAA,UAClD,OAAO;AACH,sBAAU,IAAI,UAAU,SAAU,SAAS;AACvC,kBAAI,CAAC,QAAQ,WAAW;AACpB,wBAAQ,QAAQ,OAAO,GAAG;AAAA,cAC9B,OAAO;AACH,wBAAQ,cAAc,SAASA,MAAK,cAAc,CAAC;AAAA,cACvD;AAAA,YACJ,CAAC,EAAE,KAAK,SAAU,WAAW;AACzB,uBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,oBAAIE,OAAM,aAAa,IAAI,CAAC;AAE5B,oBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAC9B,+BAAa,WAAWA,IAAG;AAAA,gBAC/B;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AAEA,0BAAgB,SAAS,QAAQ;AACjC,iBAAO;AAAA,QACX;AAEA,YAAI,sBAAsB;AAAA,UACtB,SAAS;AAAA,UACT,cAAc;AAAA,UACd,UAAU,oBAAoB;AAAA,UAC9B,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAEA,YAAI,YAAY,SAASY,WAAU,GAAG,GAAG;AACrC,iBAAO,MAAM,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC;AAAA,QAC3F;AAEA,YAAI,WAAW,SAASC,UAAS,OAAO,eAAe;AACnD,cAAI,MAAM,MAAM;AAChB,cAAI,IAAI;AACR,iBAAO,IAAI,KAAK;AACZ,gBAAI,UAAU,MAAM,CAAC,GAAG,aAAa,GAAG;AACpC,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AAEA,iBAAO;AAAA,QACX;AAEA,YAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC1C,iBAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,QACnD;AAIA,YAAI,iBAAiB,CAAC;AAEtB,YAAI,gBAAgB,CAAC;AAErB,YAAI,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAEA,YAAI,qBAAqB,CAAC,eAAe,UAAU,SAAS,eAAe,OAAO,SAAS,eAAe,aAAa,OAAO;AAE9H,YAAI,wBAAwB,CAAC,cAAc;AAE3C,YAAI,iBAAiB,CAAC,SAAS,WAAW,WAAW,OAAO,QAAQ,UAAU,cAAc,SAAS,EAAE,OAAO,qBAAqB;AAEnI,YAAI,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,QAAQ,mBAAmB,MAAM;AAAA,UACjC,MAAM;AAAA;AAAA;AAAA,UAGN,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACb;AAEA,iBAAS,cAAc,qBAAqB,eAAe;AACvD,8BAAoB,aAAa,IAAI,WAAY;AAC7C,gBAAI,QAAQ;AACZ,mBAAO,oBAAoB,MAAM,EAAE,KAAK,WAAY;AAChD,qBAAO,oBAAoB,aAAa,EAAE,MAAM,qBAAqB,KAAK;AAAA,YAC9E,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,iBAAS,SAAS;AACd,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,gBAAI,MAAM,UAAU,CAAC;AAErB,gBAAI,KAAK;AACL,uBAAS,QAAQ,KAAK;AAClB,oBAAI,IAAI,eAAe,IAAI,GAAG;AAC1B,sBAAI,QAAQ,IAAI,IAAI,CAAC,GAAG;AACpB,8BAAU,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,MAAM;AAAA,kBACzC,OAAO;AACH,8BAAU,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI;AAAA,kBACjC;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO,UAAU,CAAC;AAAA,QACtB;AAEA,YAAI,cAAc,WAAY;AAC1B,mBAASC,aAAY,SAAS;AAC1B,4BAAgB,MAAMA,YAAW;AAEjC,qBAAS,iBAAiB,gBAAgB;AACtC,kBAAI,eAAe,eAAe,aAAa,GAAG;AAC9C,oBAAI,SAAS,eAAe,aAAa;AACzC,oBAAI,aAAa,OAAO;AACxB,qBAAK,aAAa,IAAI;AAEtB,oBAAI,CAAC,eAAe,UAAU,GAAG;AAI7B,uBAAK,aAAa,MAAM;AAAA,gBAC5B;AAAA,cACJ;AAAA,YACJ;AAEA,iBAAK,iBAAiB,OAAO,CAAC,GAAG,aAAa;AAC9C,iBAAK,UAAU,OAAO,CAAC,GAAG,KAAK,gBAAgB,OAAO;AACtD,iBAAK,aAAa;AAClB,iBAAK,cAAc;AACnB,iBAAK,SAAS;AACd,iBAAK,UAAU;AAEf,iBAAK,6BAA6B;AAClC,iBAAK,UAAU,KAAK,QAAQ,MAAM,EAAE,OAAO,EAAE,WAAY;AAAA,YAAC,CAAC;AAAA,UAC/D;AAQA,UAAAA,aAAY,UAAU,SAAS,SAAS,OAAO,SAAS;AAIpD,iBAAK,OAAO,YAAY,cAAc,cAAc,QAAQ,OAAO,OAAO,UAAU;AAGhF,kBAAI,KAAK,QAAQ;AACb,uBAAO,IAAI,MAAM,sDAA2D;AAAA,cAChF;AAEA,uBAAS,KAAK,SAAS;AACnB,oBAAI,MAAM,aAAa;AACnB,0BAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,OAAO,GAAG;AAAA,gBAC9C;AAEA,oBAAI,MAAM,aAAa,OAAO,QAAQ,CAAC,MAAM,UAAU;AACnD,yBAAO,IAAI,MAAM,oCAAoC;AAAA,gBACzD;AAEA,qBAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,cAC/B;AAIA,kBAAI,YAAY,WAAW,QAAQ,QAAQ;AACvC,uBAAO,KAAK,UAAU,KAAK,QAAQ,MAAM;AAAA,cAC7C;AAEA,qBAAO;AAAA,YACX,WAAW,OAAO,YAAY,UAAU;AACpC,qBAAO,KAAK,QAAQ,OAAO;AAAA,YAC/B,OAAO;AACH,qBAAO,KAAK;AAAA,YAChB;AAAA,UACJ;AAMA,UAAAA,aAAY,UAAU,eAAe,SAAS,aAAa,cAAc,UAAU,eAAe;AAC9F,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnD,kBAAI;AACA,oBAAI,aAAa,aAAa;AAC9B,oBAAI,kBAAkB,IAAI,MAAM,sFAA2F;AAI3H,oBAAI,CAAC,aAAa,SAAS;AACvB,yBAAO,eAAe;AACtB;AAAA,gBACJ;AAEA,oBAAI,gBAAgB,eAAe,OAAO,cAAc;AACxD,yBAAS,IAAI,GAAG,MAAM,cAAc,QAAQ,IAAI,KAAK,KAAK;AACtD,sBAAI,mBAAmB,cAAc,CAAC;AAItC,sBAAI,aAAa,CAAC,SAAS,uBAAuB,gBAAgB;AAClE,uBAAK,cAAc,aAAa,gBAAgB,MAAM,OAAO,aAAa,gBAAgB,MAAM,YAAY;AACxG,2BAAO,eAAe;AACtB;AAAA,kBACJ;AAAA,gBACJ;AAEA,oBAAI,0BAA0B,SAASC,2BAA0B;AAC7D,sBAAI,8BAA8B,SAASC,6BAA4B,YAAY;AAC/E,2BAAO,WAAY;AACf,0BAAI,QAAQ,IAAI,MAAM,YAAY,aAAa,2CAA2C;AAC1F,0BAAIC,WAAU,UAAU,OAAO,KAAK;AACpC,sCAAgBA,UAAS,UAAU,UAAU,SAAS,CAAC,CAAC;AACxD,6BAAOA;AAAA,oBACX;AAAA,kBACJ;AAEA,2BAAS,KAAK,GAAG,OAAO,sBAAsB,QAAQ,KAAK,MAAM,MAAM;AACnE,wBAAI,uBAAuB,sBAAsB,EAAE;AACnD,wBAAI,CAAC,aAAa,oBAAoB,GAAG;AACrC,mCAAa,oBAAoB,IAAI,4BAA4B,oBAAoB;AAAA,oBACzF;AAAA,kBACJ;AAAA,gBACJ;AAEA,wCAAwB;AAExB,oBAAI,mBAAmB,SAASC,kBAAiB,SAAS;AACtD,sBAAI,eAAe,UAAU,GAAG;AAC5B,4BAAQ,KAAK,oCAAoC,UAAU;AAAA,kBAC/D;AACA,iCAAe,UAAU,IAAI;AAC7B,gCAAc,UAAU,IAAI;AAI5B,0BAAQ;AAAA,gBACZ;AAEA,oBAAI,cAAc,cAAc;AAC5B,sBAAI,aAAa,YAAY,OAAO,aAAa,aAAa,YAAY;AACtE,iCAAa,SAAS,EAAE,KAAK,kBAAkB,MAAM;AAAA,kBACzD,OAAO;AACH,qCAAiB,CAAC,CAAC,aAAa,QAAQ;AAAA,kBAC5C;AAAA,gBACJ,OAAO;AACH,mCAAiB,IAAI;AAAA,gBACzB;AAAA,cACJ,SAAS,GAAG;AACR,uBAAO,CAAC;AAAA,cACZ;AAAA,YACJ,CAAC;AAED,gCAAoB,SAAS,UAAU,aAAa;AACpD,mBAAO;AAAA,UACX;AAEA,UAAAJ,aAAY,UAAU,SAAS,SAAS,SAAS;AAC7C,mBAAO,KAAK,WAAW;AAAA,UAC3B;AAEA,UAAAA,aAAY,UAAU,YAAY,SAAS,UAAU,YAAY,UAAU,eAAe;AACtF,gBAAI,mBAAmB,eAAe,UAAU,IAAI,UAAU,QAAQ,eAAe,UAAU,CAAC,IAAI,UAAU,OAAO,IAAI,MAAM,mBAAmB,CAAC;AAEnJ,gCAAoB,kBAAkB,UAAU,aAAa;AAC7D,mBAAO;AAAA,UACX;AAEA,UAAAA,aAAY,UAAU,gBAAgB,SAAS,cAAc,UAAU;AACnE,gBAAI,oBAAoB,UAAU,QAAQ,qBAAqB;AAC/D,gCAAoB,mBAAmB,QAAQ;AAC/C,mBAAO;AAAA,UACX;AAEA,UAAAA,aAAY,UAAU,QAAQ,SAAS,MAAM,UAAU;AACnD,gBAAIhB,QAAO;AAEX,gBAAI,UAAUA,MAAK,WAAW,KAAK,WAAY;AAC3C,kBAAIA,MAAK,WAAW,MAAM;AACtB,gBAAAA,MAAK,SAASA,MAAK,YAAY;AAAA,cACnC;AAEA,qBAAOA,MAAK;AAAA,YAChB,CAAC;AAED,gCAAoB,SAAS,UAAU,QAAQ;AAC/C,mBAAO;AAAA,UACX;AAEA,UAAAgB,aAAY,UAAU,YAAY,SAAS,UAAU,SAAS,UAAU,eAAe;AACnF,gBAAIhB,QAAO;AAEX,gBAAI,CAAC,QAAQ,OAAO,GAAG;AACnB,wBAAU,CAAC,OAAO;AAAA,YACtB;AAEA,gBAAI,mBAAmB,KAAK,qBAAqB,OAAO;AAExD,qBAAS,oBAAoB;AACzB,cAAAA,MAAK,QAAQ,SAASA,MAAK,OAAO;AAAA,YACtC;AAEA,qBAAS,qBAAqB,QAAQ;AAClC,cAAAA,MAAK,QAAQ,MAAM;AACnB,gCAAkB;AAElB,cAAAA,MAAK,SAASA,MAAK,aAAaA,MAAK,OAAO;AAC5C,qBAAOA,MAAK;AAAA,YAChB;AAEA,qBAAS,WAAWqB,mBAAkB;AAClC,qBAAO,WAAY;AACf,oBAAI,qBAAqB;AAEzB,yBAAS,oBAAoB;AACzB,yBAAO,qBAAqBA,kBAAiB,QAAQ;AACjD,wBAAI,aAAaA,kBAAiB,kBAAkB;AACpD;AAEA,oBAAArB,MAAK,UAAU;AACf,oBAAAA,MAAK,SAAS;AAEd,2BAAOA,MAAK,UAAU,UAAU,EAAE,KAAK,oBAAoB,EAAE,OAAO,EAAE,iBAAiB;AAAA,kBAC3F;AAEA,oCAAkB;AAClB,sBAAI,QAAQ,IAAI,MAAM,oCAAoC;AAC1D,kBAAAA,MAAK,aAAa,UAAU,OAAO,KAAK;AACxC,yBAAOA,MAAK;AAAA,gBAChB;AAEA,uBAAO,kBAAkB;AAAA,cAC7B;AAAA,YACJ;AAKA,gBAAI,mBAAmB,KAAK,eAAe,OAAO,KAAK,WAAW,OAAO,EAAE,WAAY;AACnF,qBAAO,UAAU,QAAQ;AAAA,YAC7B,CAAC,IAAI,UAAU,QAAQ;AAEvB,iBAAK,aAAa,iBAAiB,KAAK,WAAY;AAChD,kBAAI,aAAa,iBAAiB,CAAC;AACnC,cAAAA,MAAK,UAAU;AACf,cAAAA,MAAK,SAAS;AAEd,qBAAOA,MAAK,UAAU,UAAU,EAAE,KAAK,SAAU,QAAQ;AACrD,gBAAAA,MAAK,UAAU,OAAO;AACtB,kCAAkB;AAClB,gBAAAA,MAAK,6BAA6B;AAClC,gBAAAA,MAAK,cAAc,WAAW,gBAAgB;AAAA,cAClD,CAAC;AAAA,YACL,CAAC,EAAE,OAAO,EAAE,WAAY;AACpB,gCAAkB;AAClB,kBAAI,QAAQ,IAAI,MAAM,oCAAoC;AAC1D,cAAAA,MAAK,aAAa,UAAU,OAAO,KAAK;AACxC,qBAAOA,MAAK;AAAA,YAChB,CAAC;AAED,gCAAoB,KAAK,YAAY,UAAU,aAAa;AAC5D,mBAAO,KAAK;AAAA,UAChB;AAEA,UAAAgB,aAAY,UAAU,WAAW,SAAS,SAAS,YAAY;AAC3D,mBAAO,CAAC,CAAC,cAAc,UAAU;AAAA,UACrC;AAEA,UAAAA,aAAY,UAAU,UAAU,SAAS,QAAQ,6BAA6B;AAC1E,mBAAO,MAAM,2BAA2B;AAAA,UAC5C;AAEA,UAAAA,aAAY,UAAU,uBAAuB,SAAS,qBAAqB,SAAS;AAChF,gBAAI,mBAAmB,CAAC;AACxB,qBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAChD,kBAAI,aAAa,QAAQ,CAAC;AAC1B,kBAAI,KAAK,SAAS,UAAU,GAAG;AAC3B,iCAAiB,KAAK,UAAU;AAAA,cACpC;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAEA,UAAAA,aAAY,UAAU,+BAA+B,SAAS,+BAA+B;AAKzF,qBAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACvD,4BAAc,MAAM,eAAe,CAAC,CAAC;AAAA,YACzC;AAAA,UACJ;AAEA,UAAAA,aAAY,UAAU,iBAAiB,SAAS,eAAe,SAAS;AACpE,mBAAO,IAAIA,aAAY,OAAO;AAAA,UAClC;AAEA,iBAAOA;AAAA,QACX,EAAE;AAMF,YAAI,iBAAiB,IAAI,YAAY;AAErC,QAAAvB,QAAO,UAAU;AAAA,MAEjB,GAAE,EAAC,KAAI,EAAC,CAAC,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AAAA,IACrB,CAAC;AAAA;AAAA;", "names": ["define", "module", "exports", "o", "e", "n", "global", "Promise", "self", "i", "key", "length", "idb", "forage", "value", "err", "keys", "db", "t", "error", "resolve", "reject", "sameValue", "includes", "LocalForage", "configureMissingMethods", "methodNotImplementedFactory", "promise", "setDriverSupport", "supportedDrivers"]}