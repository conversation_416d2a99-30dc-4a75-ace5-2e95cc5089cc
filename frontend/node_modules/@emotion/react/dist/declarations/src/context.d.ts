import * as React from 'react';
import { EmotionCache } from '@emotion/cache';
export declare let CacheProvider: React.Provider<EmotionCache | null>;
export declare let __unsafe_useEmotionCache: () => EmotionCache | null;
declare let withEmotionCache: <Props, RefType = any>(func: (props: React.PropsWithoutRef<Props>, context: EmotionCache, ref?: React.ForwardedRef<RefType>) => React.ReactNode) => React.FC<React.PropsWithoutRef<Props> & React.RefAttributes<RefType>> | React.ForwardRefExoticComponent<React.PropsWithoutRef<Props> & React.RefAttributes<RefType>>;
export { withEmotionCache };
