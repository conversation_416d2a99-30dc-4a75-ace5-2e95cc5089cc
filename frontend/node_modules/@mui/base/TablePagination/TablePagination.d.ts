import { PolymorphicComponent } from '../utils';
import { TablePaginationTypeMap } from './TablePagination.types';
/**
 * A pagination for tables.
 *
 * Demos:
 *
 * - [Table Pagination](https://mui.com/base-ui/react-table-pagination/)
 *
 * API:
 *
 * - [TablePagination API](https://mui.com/base-ui/react-table-pagination/components-api/#table-pagination)
 */
declare const TablePagination: PolymorphicComponent<TablePaginationTypeMap<{}, "td">>;
export { TablePagination };
