{"version": 3, "file": "zod.umd.js", "sources": ["../src/zod.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport { ZodError, z } from 'zod';\nimport type { Resolver } from './types';\n\nconst isZodError = (error: any): error is ZodError =>\n  Array.isArray(error?.errors);\n\nconst parseErrorSchema = (\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n};\n\nexport const zodResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "_catch", "Array", "isArray", "isZodError", "toNestErrors", "criteriaMode", "reject"], "mappings": "wXAKA,IAGMA,EAAmB,SACvBC,EACAC,GAGA,IADA,IAAMC,EAAqC,CAAE,EACtCF,EAAUG,QAAU,CACzB,IAAMC,EAAQJ,EAAU,GAChBK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,KAAK,KAExB,IAAKP,EAAOK,GACV,GAAI,gBAAiBH,EAAO,CAC1B,IAAMM,EAAaN,EAAMO,YAAY,GAAGT,OAAO,GAE/CA,EAAOK,GAAS,CACdD,QAASI,EAAWJ,QACpBM,KAAMF,EAAWL,KAErB,MACEH,EAAOK,GAAS,CAAED,QAAAA,EAASM,KAAMP,GAUrC,GANI,gBAAiBD,GACnBA,EAAMO,YAAYE,QAAQ,SAACH,GAAU,OACnCA,EAAWR,OAAOW,QAAQ,SAACC,GAAM,OAAAd,EAAUe,KAAKD,EAAE,EAAC,GAInDb,EAA0B,CAC5B,IAAMe,EAAQd,EAAOK,GAAOS,MACtBC,EAAWD,GAASA,EAAMZ,EAAMC,MAEtCH,EAAOK,GAASW,EAAAA,aACdX,EACAN,EACAC,EACAG,EACAY,EACK,GAAgBE,OAAOF,EAAsBb,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAUoB,OACZ,CAEA,OAAOlB,CACT,gBAGE,SAACmB,EAAQC,EAAeC,GACjBC,YADgC,IAAfD,IAAAA,EAAkB,CAAE,GACrCC,SAAAA,EAAQC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,gCACvBD,QAAAC,QACiBP,EACQ,SAAzBE,EAAgBM,KAAkB,QAAU,cAC5CL,EAAQF,IAAcQ,KAFlBC,SAAAA,GAMN,OAFAL,EAAQM,2BAA6BC,yBAAuB,CAAA,EAAIP,GAEzD,CACLxB,OAAQ,CAAiB,EACzBsB,OAAQD,EAAgBW,IAAMV,EAASO,EACvC,4DAXuBI,CACvB,EAWH,SAAQ/B,GACP,GApEa,SAACA,GAClB,OAAAgC,MAAMC,QAAa,MAALjC,OAAK,EAALA,EAAOF,OAAO,CAmEpBoC,CAAWlC,GACb,MAAO,CACLoB,OAAQ,CAAA,EACRtB,OAAQqC,EAAYA,aAClBxC,EACEK,EAAMF,QACLwB,EAAQM,2BACkB,QAAzBN,EAAQc,cAEZd,IAKN,MAAMtB,CACR,GACF,CAAC,MAAAU,GAAA,OAAAa,QAAAc,OAAA3B,EAAA,CAAA,CAAA"}