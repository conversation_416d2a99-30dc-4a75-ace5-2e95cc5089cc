import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import{errors as t}from"@vinejs/vine";import{appendErrors as o}from"react-hook-form";var n=function(e,r){for(var t={};e.length;){var n=e[0],i=n.field;if(i in t||(t[i]={message:n.message,type:n.rule}),r){var s=t[i].types,a=s&&s[n.rule];t[i]=o(i,r,t,n.rule,a?[].concat(a,[n.message]):n.message)}e.shift()}return t},i=function(o,i,s){return void 0===s&&(s={}),function(a,u,f){try{return Promise.resolve(function(r,t){try{var n=Promise.resolve(o.validate(a,i)).then(function(r){return f.shouldUseNativeValidation&&e({},f),{errors:{},values:s.raw?a:r}})}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}(0,function(e){if(e instanceof t.E_VALIDATION_ERROR)return{values:{},errors:r(n(e.messages,!f.shouldUseNativeValidation&&"all"===f.criteriaMode),f)};throw e}))}catch(e){return Promise.reject(e)}}};export{i as vineResolver};
//# sourceMappingURL=vine.module.js.map
