!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("@vinejs/vine"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","@vinejs/vine","react-hook-form"],r):r((e||self).hookformResolversVine={},e.hookformResolvers,e.vine,e.ReactHookForm)}(this,function(e,r,o,t){var n=function(e,r){for(var o={};e.length;){var n=e[0],i=n.field;if(i in o||(o[i]={message:n.message,type:n.rule}),r){var s=o[i].types,a=s&&s[n.rule];o[i]=t.appendErrors(i,r,o,n.rule,a?[].concat(a,[n.message]):n.message)}e.shift()}return o};e.vineResolver=function(e,t,i){return void 0===i&&(i={}),function(s,a,f){try{return Promise.resolve(function(o,n){try{var a=Promise.resolve(e.validate(s,t)).then(function(e){return f.shouldUseNativeValidation&&r.validateFieldsNatively({},f),{errors:{},values:i.raw?s:e}})}catch(e){return n(e)}return a&&a.then?a.then(void 0,n):a}(0,function(e){if(e instanceof o.errors.E_VALIDATION_ERROR)return{values:{},errors:r.toNestErrors(n(e.messages,!f.shouldUseNativeValidation&&"all"===f.criteriaMode),f)};throw e}))}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=vine.umd.js.map
