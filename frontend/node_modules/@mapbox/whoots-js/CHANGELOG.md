_Breaking changes, which may affect downstream projects are marked with a_ :warning:

## 3.1.0
##### 2018-Jul-12
* Replace legacy Rollup `jsnext:main` with now standard `module` ([#74])
* :warning: Drop support for Node 4

[#74]: https://github.com/mapbox/whoots-js/issues/74

## 3.0.0
##### 2017-Feb-13
* :warning: whoots-js is now a scoped package under the @mapbox namespace

## 2.1.0
##### Jul 15, 2016
* Release as ES6 module alongside UMD build, add `jsnext:main` to `package.json`

## 2.0.0
##### Jun 1, 2016
* :warning: Refactor for a classless API

## 1.1.0
##### May 23, 2016
* Make getTileBbox and getMercCoords public

## 1.0.0
##### May 23, 2016
* Initial release
