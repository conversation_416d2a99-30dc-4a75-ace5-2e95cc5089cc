{"name": "@mapbox/unitbezier", "version": "0.0.1", "description": "unit bezier curve interpolation", "main": "index.js", "typings": "index.d.ts", "scripts": {"pretest": "eslint index.js test/*.js", "test": "node test/unitbezier.js"}, "files": ["index.js", "index.d.ts"], "repository": {"type": "git", "url": "**************:mapbox/unitbezier.git"}, "keywords": ["unit", "bezier", "interpolation", "webkit"], "author": "", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/unitbezier/issues"}, "homepage": "https://github.com/mapbox/unitbezier", "devDependencies": {"eslint": "^8.0.1", "eslint-config-mourner": "^2.0.3", "tape": "^5.3.1"}, "eslintConfig": {"extends": "mourner"}}