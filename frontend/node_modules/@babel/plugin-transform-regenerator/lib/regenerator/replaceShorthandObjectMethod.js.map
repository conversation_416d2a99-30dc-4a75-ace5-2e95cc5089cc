{"version": 3, "names": ["util", "require", "replaceShorthandObjectMethod", "path", "t", "getTypes", "node", "isFunction", "Error", "isObjectMethod", "generator", "parameters", "params", "map", "param", "cloneDeep", "functionExpression", "body", "async", "replaceWithOrRemove", "objectProperty", "key", "computed", "get"], "sources": ["../../src/regenerator/replaceShorthandObjectMethod.ts"], "sourcesContent": ["import * as util from \"./util.ts\";\n\n// this function converts a shorthand object generator method into a normal\n// (non-shorthand) object property which is a generator function expression. for\n// example, this:\n//\n//  var foo = {\n//    *bar(baz) { return 5; }\n//  }\n//\n// should be replaced with:\n//\n//  var foo = {\n//    bar: function*(baz) { return 5; }\n//  }\n//\n// to do this, it clones the parameter array and the body of the object generator\n// method into a new FunctionExpression.\n//\n// this method can be passed any Function AST node path, and it will return\n// either:\n//   a) the path that was passed in (iff the path did not need to be replaced) or\n//   b) the path of the new FunctionExpression that was created as a replacement\n//     (iff the path did need to be replaced)\n//\n// In either case, though, the caller can count on the fact that the return value\n// is a Function AST node path.\n//\n// If this function is called with an AST node path that is not a Function (or with an\n// argument that isn't an AST node path), it will throw an error.\nexport default function replaceShorthandObjectMethod(path: any) {\n  const t = util.getTypes();\n\n  if (!path.node || !t.isFunction(path.node)) {\n    throw new Error(\n      \"replaceShorthandObjectMethod can only be called on Function AST node paths.\",\n    );\n  }\n\n  // this function only replaces shorthand object methods (called ObjectMethod\n  // in Babel-speak).\n  if (!t.isObjectMethod(path.node)) {\n    return path;\n  }\n\n  // this function only replaces generators.\n  if (!path.node.generator) {\n    return path;\n  }\n\n  const parameters = path.node.params.map(function (param: any) {\n    return t.cloneDeep(param);\n  });\n\n  const functionExpression = t.functionExpression(\n    null, // id\n    parameters, // params\n    t.cloneDeep(path.node.body), // body\n    path.node.generator,\n    path.node.async,\n  );\n\n  util.replaceWithOrRemove(\n    path,\n    t.objectProperty(\n      t.cloneDeep(path.node.key), // key\n      functionExpression, //value\n      path.node.computed, // computed\n      false, // shorthand\n    ),\n  );\n\n  // path now refers to the ObjectProperty AST node path, but we want to return a\n  // Function AST node path for the function expression we created. we know that\n  // the FunctionExpression we just created is the value of the ObjectProperty,\n  // so return the \"value\" path off of this path.\n  return path.get(\"value\");\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,IAAA,GAAAC,OAAA;AA8Be,SAASC,4BAA4BA,CAACC,IAAS,EAAE;EAC9D,MAAMC,CAAC,GAAGJ,IAAI,CAACK,QAAQ,CAAC,CAAC;EAEzB,IAAI,CAACF,IAAI,CAACG,IAAI,IAAI,CAACF,CAAC,CAACG,UAAU,CAACJ,IAAI,CAACG,IAAI,CAAC,EAAE;IAC1C,MAAM,IAAIE,KAAK,CACb,6EACF,CAAC;EACH;EAIA,IAAI,CAACJ,CAAC,CAACK,cAAc,CAACN,IAAI,CAACG,IAAI,CAAC,EAAE;IAChC,OAAOH,IAAI;EACb;EAGA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACI,SAAS,EAAE;IACxB,OAAOP,IAAI;EACb;EAEA,MAAMQ,UAAU,GAAGR,IAAI,CAACG,IAAI,CAACM,MAAM,CAACC,GAAG,CAAC,UAAUC,KAAU,EAAE;IAC5D,OAAOV,CAAC,CAACW,SAAS,CAACD,KAAK,CAAC;EAC3B,CAAC,CAAC;EAEF,MAAME,kBAAkB,GAAGZ,CAAC,CAACY,kBAAkB,CAC7C,IAAI,EACJL,UAAU,EACVP,CAAC,CAACW,SAAS,CAACZ,IAAI,CAACG,IAAI,CAACW,IAAI,CAAC,EAC3Bd,IAAI,CAACG,IAAI,CAACI,SAAS,EACnBP,IAAI,CAACG,IAAI,CAACY,KACZ,CAAC;EAEDlB,IAAI,CAACmB,mBAAmB,CACtBhB,IAAI,EACJC,CAAC,CAACgB,cAAc,CACdhB,CAAC,CAACW,SAAS,CAACZ,IAAI,CAACG,IAAI,CAACe,GAAG,CAAC,EAC1BL,kBAAkB,EAClBb,IAAI,CAACG,IAAI,CAACgB,QAAQ,EAClB,KACF,CACF,CAAC;EAMD,OAAOnB,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC;AAC1B", "ignoreList": []}