{"name": "@maplibre/maplibre-gl-style-spec", "description": "a specification for maplibre styles", "version": "19.3.3", "author": "MapLibre", "keywords": ["mapbox", "mapbox-gl", "mapbox-gl-js", "maplibre", "maplibre-gl", "maplibre-gl-js"], "license": "ISC", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "scripts": {"build": "rollup --configPlugin @rollup/plugin-typescript -c rollup.config.ts", "generate-style-spec": "ts-node build/generate-style-spec.ts", "generate-typings": "ts-node build/generate-typings.ts", "test-build": "jest --selectProjects=build", "test-integration": "jest --selectProjects=integration", "test-unit": "jest --selectProjects=unit", "jest": "jest", "jest-ci": "jest --reporters=github-actions --reporters=summary", "compile": "tsc", "lint": "eslint --cache --ext .ts,.tsx --ignore-path .gitignore .", "typecheck": "tsc --noEmit", "prepare": "npm run generate-style-spec"}, "repository": {"type": "git", "url": "**************:maplibre/maplibre-gl-style-spec.git"}, "bin": {"gl-style-migrate": "dist/gl-style-migrate.mjs", "gl-style-validate": "dist/gl-style-validate.mjs", "gl-style-format": "dist/gl-style-format.mjs"}, "dependencies": {"@mapbox/jsonlint-lines-primitives": "~2.0.2", "@mapbox/unitbezier": "^0.0.1", "json-stringify-pretty-compact": "^3.0.0", "minimist": "^1.2.8", "rw": "^1.3.3", "sort-object": "^3.0.3"}, "sideEffects": false, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.0.1", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.4", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.5", "@types/eslint": "^8.44.4", "@types/geojson": "^7946.0.11", "@types/jest": "^29.5.5", "@types/node": "^20.8.6", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.62.0", "dts-bundle-generator": "^8.0.1", "eslint": "^8.51.0", "eslint-config-mourner": "^3.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.2", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-react": "^7.33.2", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "rollup": "^4.1.3", "rollup-plugin-preserve-shebang": "^1.0.1", "rollup-plugin-sourcemaps": "^0.6.3", "semver": "^7.5.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tslib": "^2.6.2", "typescript": "^5.2.2"}}