@import '/src/_variables.scss';

@media (min-width: 800px) {
  .side_menu_button {
    display: none;
  }
}

.side_menu_button {
  margin-left: 20px;
  padding: 10px;
  font-size: 30px;
  cursor: pointer;
}

.header {
  width: 100%;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  overflow: hidden;
  z-index: 200;
  background-color: white;
  box-shadow: 0 1px 2px 0 #0000001a;
  height: 3.75rem;
  box-sizing: border-box;

  .logo_container {
    padding: calc(1rem * 0.5) 1rem;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    .logo {
      cursor: pointer;
      display: block;
      width: auto;
      height: 30px;
    }
  }
}

.title_container {
  height: 30px;
  margin-left: 10px;
  width: 250px;
}

.title {
  cursor: pointer;
  width: 250px;
  font-size: 1.7rem;
  font-weight: 400;
  color: #333;
}
