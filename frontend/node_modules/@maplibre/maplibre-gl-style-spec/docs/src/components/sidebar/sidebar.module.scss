@import '../../_variables.scss';

.sidebar_outer_container {
  clip-path: inset(0);
  display: block;
  will-change: width;
  border: 1px solid rgb(236, 242, 247);
  background-color: white;
  margin-top: calc(3.75rem * -1);

  .sidebar_viewport {
    position: sticky;
    top: 0;
    height: 100%;
    max-height: 100vh;
    box-sizing: border-box;
  }

  .sidebar_inner_container {
    display: flex;
    width: 200px;
    padding-top: $--header-height;
    box-sizing: border-box;
    flex-direction: column;
    height: 100%;
  }

  .nav_items {
    padding: 1rem;
    flex-grow: 1;
    align-items: flex-start;
    width: 100%;
    overflow: auto;

    ul {
      list-style-type: none;
      padding: 0;
      margin: 0;

      li {
        a {
          text-decoration: none;
          color: #333;
          font-weight: 500;
          font-size: 0.9rem;
          line-height: 1.4;
          padding: 6px 12px;
          border-radius: 4px;
          display: block;

          &:hover {
            color: #007bff;
            background-color: #f0f0f0;
          }
        }
      }
    }
  }
}

@media (max-width: 800px) {
  .sidebar_outer_container {
    display: none;
  }
}
