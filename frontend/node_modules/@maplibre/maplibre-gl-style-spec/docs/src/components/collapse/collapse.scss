@media (min-width: 1050px) {
  .collapsible {
    display: none;
  }
}

.collapsible__trigger {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 14px;
  font-weight: 600;
  background-color: white;
  border: 1px solid hsl(0, 0%, 86%);
  color: hsl(240 4% 16%);
  text-align: left;
  outline: none;
}
.collapsible__trigger:focus-visible {
  outline: 2px solid hsl(200 98% 39%);
  outline-offset: 2px;
}
.collapsible__trigger-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: transform 250ms;
}
.collapsible__trigger[data-expanded] .collapsible__trigger-icon {
  transform: rotateZ(180deg);
}
.collapsible__content {
  width: 100%;
  overflow: hidden;
  border: 1px solid hsl(0, 0%, 86%);
  border-top: none;
  color: hsl(240 4% 16%);

  .toc_viewport {
    padding: 0 !important;
  }

  ul {
    width: 100%;

    a {
      margin: 0px 5px;
    }
  }
}

.collapsible__content-text {
  padding: 16px;
}
@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--kb-collapsible-content-height);
  }
}
@keyframes slideUp {
  from {
    height: var(--kb-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
