/**
 * atom-dark theme for `prism.js`, adjusted for maplibre style repo
 * Based on Atom's `atom-dark` theme: https://github.com/atom/atom-dark-syntax
 * <AUTHOR> (@gibsjose)
 */

code[class*='language-'],
pre[class*='language-'] {
  border-radius: 5px;
  color: #c5c8c6;
  font-size: 10pt;
  text-shadow: none;
  font-family: Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;

  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;

  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

a {
  margin-right: 0;
  text-decoration: none;
}

p a {
  margin-right: 0;
  text-decoration: none;
  color: rgb(0, 132, 255);
  font-style: italic;
}

.color-gray {
  color: rgb(91, 120, 151);
  font-style: italic;
  font-size: 15px;
  font-family: 'Open Sans', sans-serif;
  a {
    text-decoration: underline !important;
    color: rgb(0, 132, 255) !important;
  }
}

h3 a,
h2 a,
h1 a {
  font-weight: 300;
  color: rgb(31, 51, 73);

  &:hover {
    color: #1351ce;
  }

  &:focus-visible {
    outline: none;
  }
}

h3,
h2,
h1 {
  &:focus-visible {
    outline: none;
  }
}

pre[class*='language-'] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
}

:not(pre) > code[class*='language-'],
pre[class*='language-'] {
  background: #1d1f21;
}

/* Inline code */
:not(pre) > code[class*='language-'] {
  padding: 0.1em;
  color: #3c39c7;
  background: none;

  a {
    text-decoration: none;
  }

  span {
    color: #3c39c7 !important;
  }
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #7c7c7c;
}

.token.punctuation {
  color: #c5c8c6;
}

.namespace {
  opacity: 0.7;
}

.token.property,
.token.keyword,
.token.tag {
  color: #96cbfe;
}

.token.class-name {
  color: #ffffb6;
  text-decoration: underline;
}

.token.boolean,
.token.constant {
  color: #99cc99;
}

.token.symbol,
.token.deleted {
  color: #f92672;
}

.token.number {
  color: #ff73fd;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #a8ff60;
}

.token.variable {
  color: #c6c5fe;
}

.token.operator {
  color: #ededed;
  background: none;
}

.token.entity {
  color: #ffffb6;
  cursor: help;
}

.token.url {
  color: #96cbfe;
}

.language-css .token.string,
.style .token.string {
  color: #87c38a;
}

.token.atrule,
.token.attr-value {
  color: #f9ee98;
}

.token.function {
  color: #dad085;
}

.token.regex {
  color: #e9c062;
}

.token.important {
  color: #fd971f;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}
