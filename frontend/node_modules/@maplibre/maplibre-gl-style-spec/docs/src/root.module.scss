@import '/src/_variables.scss';

div {
  box-sizing: border-box;
}

body {
  font-family: 'Open sans', sans-serif;
  font-size: 15px;
  line-height: 24px !important;
  color: #1f3349;
  margin: 0px;
}

p {
  margin: 14px 0 !important;
  font-weight: 400 !important;
}

h1 {
  margin-top: 6px;
  margin-bottom: 30px;
  font-size: 36px;
  font-weight: 700;
  line-height: 54px;
}

h2 {
  font-weight: 400 !important;
  font-size: 30px;
  padding-top: 30px;
  margin: 36px 0 18px;
}

h3 {
  font-size: 18px;
  margin-bottom: 0;
}

a.anchor_h3 {
  padding-left: 40px !important;
}

h4 {
  font-size: 18px;
  margin-bottom: 0;
}

p {
  margin: 2rem auto;
  line-height: 1.35;
}

hr {
  border-color: #e0e0e0;
  width: 100%;
  border: 0;
  border-top: 1px solid #ccc;
}

@media (min-width: 480px) {
  h1 {
    max-width: none;
  }

  p {
    max-width: none;
  }
}
