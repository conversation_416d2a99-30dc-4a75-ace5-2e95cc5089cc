{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": true, "isolatedModules": false, "noEmit": false, "declaration": true, "esModuleInterop": true, "importHelpers": false, "jsx": "react", "outDir": "tsc", "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "skipLibCheck": false, "sourceMap": true, "strict": false, "target": "ES2019", "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["node", "jest", "g<PERSON><PERSON><PERSON>"]}, "ts-node": {"experimentalSpecifierResolution": "node", "esm": true}, "include": ["rollup.config.*", "src/**/*", "build/**/*"], "exclude": ["node_modules", "dist"]}