name: Docs - Build and Deploy

on:
  push:
    branches:
    - main

jobs:
  build-test-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v3
      with:
        node-version: 18

    - name: Install NPM packages
      run: npm ci

    - name: Build style spec
      run: |
        npm run generate-style-spec
        npm run generate-typings

    - name: Build docs
      run: |
        cd ./docs
        npm ci
        npm run build

    - name: Deploy
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        branch: gh-pages
        folder: docs/dist/public