{"name": "fieldez-loan-collection-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@mui/x-date-pickers": "^6.18.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-hot-toast": "^2.4.1", "workbox-webpack-plugin": "^7.0.0", "workbox-window": "^7.0.0", "idb": "^7.1.1", "localforage": "^1.10.0", "react-qr-code": "^2.0.12", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "recharts": "^2.8.0", "react-map-gl": "^7.1.6", "mapbox-gl": "^2.15.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-pwa": "^0.17.4", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}}