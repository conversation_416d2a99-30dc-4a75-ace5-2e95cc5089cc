import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Phone,
  LocationOn,
  Payment,
  Visibility,
  Schedule,
  Warning,
} from '@mui/icons-material';

const LeadsPage: React.FC = () => {
  // Mock data - in real app, this would come from Redux store
  const leads = [
    {
      id: '1',
      customerName: '<PERSON><PERSON>',
      loanAccount: 'LOAN001',
      overdueAmount: 25000,
      priority: 'High',
      status: 'Assigned',
      phone: '+91 **********',
      address: 'Mumbai, Maharashtra',
      deadline: '2024-01-20',
      visitCount: 0,
    },
    {
      id: '2',
      customerName: '<PERSON><PERSON><PERSON>',
      loanAccount: 'LOAN002',
      overdueAmount: 15000,
      priority: 'Medium',
      status: 'Visited',
      phone: '+91 **********',
      address: 'Pune, Maharashtra',
      deadline: '2024-01-22',
      visitCount: 2,
    },
    {
      id: '3',
      customerName: '<PERSON><PERSON> Yadav',
      loanAccount: 'LOAN003',
      overdueAmount: 45000,
      priority: 'Urgent',
      status: 'Visited',
      phone: '+91 **********',
      address: 'Delhi, Delhi',
      deadline: '2024-01-17',
      visitCount: 3,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'assigned':
        return 'primary';
      case 'visited':
        return 'warning';
      case 'collected':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const isOverdue = (deadline: string) => {
    return new Date(deadline) < new Date();
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Leads
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your assigned collection leads
        </Typography>
      </Box>

      {/* Stats Summary */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="primary">
                {leads.filter(l => l.status === 'Assigned').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="warning.main">
                {leads.filter(l => l.status === 'Visited').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Visited
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="error.main">
                {leads.filter(l => isOverdue(l.deadline)).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overdue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="success.main">
                ₹{leads.reduce((sum, lead) => sum + lead.overdueAmount, 0).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Due
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Leads List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Active Leads
          </Typography>
          <List>
            {leads.map((lead, index) => (
              <React.Fragment key={lead.id}>
                <ListItem
                  sx={{
                    border: isOverdue(lead.deadline) ? '1px solid' : 'none',
                    borderColor: 'error.main',
                    borderRadius: 1,
                    mb: 1,
                  }}
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {lead.customerName.charAt(0)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {lead.customerName}
                        </Typography>
                        <Chip
                          label={lead.priority}
                          size="small"
                          color={getPriorityColor(lead.priority) as any}
                        />
                        <Chip
                          label={lead.status}
                          size="small"
                          variant="outlined"
                          color={getStatusColor(lead.status) as any}
                        />
                        {isOverdue(lead.deadline) && (
                          <Chip
                            label="OVERDUE"
                            size="small"
                            color="error"
                            icon={<Warning />}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {lead.loanAccount} • ₹{lead.overdueAmount.toLocaleString()} overdue
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Phone sx={{ fontSize: 14, mr: 0.5 }} />
                            <Typography variant="caption">
                              {lead.phone}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <LocationOn sx={{ fontSize: 14, mr: 0.5 }} />
                            <Typography variant="caption">
                              {lead.address}
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Schedule sx={{ fontSize: 14, mr: 0.5 }} />
                            <Typography variant="caption">
                              Due: {new Date(lead.deadline).toLocaleDateString()}
                            </Typography>
                          </Box>
                          <Typography variant="caption">
                            Visits: {lead.visitCount}
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Visibility />}
                      onClick={() => console.log('Visit lead:', lead.id)}
                    >
                      Visit
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<Payment />}
                      onClick={() => console.log('Collect payment:', lead.id)}
                    >
                      Collect
                    </Button>
                    <IconButton
                      size="small"
                      onClick={() => window.open(`tel:${lead.phone}`)}
                    >
                      <Phone />
                    </IconButton>
                  </Box>
                </ListItem>
                {index < leads.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LeadsPage;
