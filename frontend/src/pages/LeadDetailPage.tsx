import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography } from '@mui/material';

const LeadDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Lead Details
      </Typography>
      <Typography variant="body1">
        Lead ID: {id}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        This page will show detailed information about the lead, including customer details, loan information, visit history, and payment records.
      </Typography>
    </Box>
  );
};

export default LeadDetailPage;
