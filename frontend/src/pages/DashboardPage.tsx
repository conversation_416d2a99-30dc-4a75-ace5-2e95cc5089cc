import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  Assignment,
  Payment,
  LocationOn,
  Phone,
  Schedule,
  CheckCircle,
  Warning,
  Error,
} from '@mui/icons-material';

const DashboardPage: React.FC = () => {
  // Mock data - in real app, this would come from API
  const dashboardStats = {
    pendingLeads: 8,
    visitedLeads: 12,
    collectedLeads: 5,
    todaysVisits: 6,
    todaysPayments: 3,
    todaysCollection: 45000,
    targetAmount: 100000,
    achievementRate: 45,
  };

  const recentLeads = [
    {
      id: '1',
      customerName: '<PERSON><PERSON>',
      loanAccount: 'LOAN001',
      overdueAmount: 25000,
      priority: 'High',
      status: 'Assigned',
      phone: '+91 **********',
      address: 'Mumbai, Maharashtra',
    },
    {
      id: '2',
      customerName: '<PERSON><PERSON><PERSON>',
      loanAccount: 'LOAN002',
      overdueAmount: 15000,
      priority: 'Medium',
      status: 'Visited',
      phone: '+91 **********',
      address: 'Pune, Maharashtra',
    },
    {
      id: '3',
      customerName: 'Suresh Yadav',
      loanAccount: 'LOAN003',
      overdueAmount: 45000,
      priority: 'Urgent',
      status: 'Visited',
      phone: '+91 **********',
      address: 'Delhi, Delhi',
    },
  ];

  const recentPayments = [
    {
      id: '1',
      customerName: 'Kavita Singh',
      amount: 5000,
      method: 'Cash',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      customerName: 'Suresh Yadav',
      amount: 15000,
      method: 'UPI',
      timestamp: '1 day ago',
    },
    {
      id: '3',
      customerName: 'Suresh Yadav',
      amount: 5000,
      method: 'Card',
      timestamp: '1 day ago',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'assigned':
        return 'primary';
      case 'visited':
        return 'warning';
      case 'collected':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome back! Here's your collection overview for today.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Assignment color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Pending Leads</Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardStats.pendingLeads}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Schedule color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Visited</Typography>
              </Box>
              <Typography variant="h3" color="warning.main">
                {dashboardStats.visitedLeads}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CheckCircle color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Collected</Typography>
              </Box>
              <Typography variant="h3" color="success.main">
                {dashboardStats.collectedLeads}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Payment color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Today's Collection</Typography>
              </Box>
              <Typography variant="h3" color="info.main">
                ₹{dashboardStats.todaysCollection.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Progress Card */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Monthly Target Progress</Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">
                    ₹{dashboardStats.todaysCollection.toLocaleString()} / ₹{dashboardStats.targetAmount.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="primary">
                    {dashboardStats.achievementRate}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={dashboardStats.achievementRate}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Keep up the great work! You're {dashboardStats.achievementRate}% towards your monthly target.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {dashboardStats.todaysVisits}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Today's Visits
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {dashboardStats.todaysPayments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Payments Collected
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Leads
              </Typography>
              <List>
                {recentLeads.map((lead, index) => (
                  <React.Fragment key={lead.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {lead.customerName.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1">
                              {lead.customerName}
                            </Typography>
                            <Chip
                              label={lead.priority}
                              size="small"
                              color={getPriorityColor(lead.priority) as any}
                            />
                            <Chip
                              label={lead.status}
                              size="small"
                              variant="outlined"
                              color={getStatusColor(lead.status) as any}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {lead.loanAccount} • ₹{lead.overdueAmount.toLocaleString()} overdue
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                              <Phone sx={{ fontSize: 14, mr: 0.5 }} />
                              <Typography variant="caption" sx={{ mr: 2 }}>
                                {lead.phone}
                              </Typography>
                              <LocationOn sx={{ fontSize: 14, mr: 0.5 }} />
                              <Typography variant="caption">
                                {lead.address}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                      <IconButton size="small">
                        <Phone />
                      </IconButton>
                    </ListItem>
                    {index < recentLeads.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Payments
              </Typography>
              <List>
                {recentPayments.map((payment, index) => (
                  <React.Fragment key={payment.id}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <Payment />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle2">
                            ₹{payment.amount.toLocaleString()}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              {payment.customerName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {payment.method} • {payment.timestamp}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < recentPayments.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
