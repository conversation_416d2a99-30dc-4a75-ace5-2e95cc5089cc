import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Lead, CreateVisitRequest, CollectPaymentRequest } from '@/types';
import { leadsApi } from '@/services/api';

interface LeadsState {
  leads: Lead[];
  currentLead: Lead | null;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  offset: number;
  filters: {
    status?: string;
    date?: string;
  };
}

const initialState: LeadsState = {
  leads: [],
  currentLead: null,
  isLoading: false,
  error: null,
  hasMore: true,
  offset: 0,
  filters: {},
};

// Async thunks
export const fetchLeads = createAsyncThunk(
  'leads/fetchLeads',
  async (params: { refresh?: boolean; filters?: any } = {}, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { leads: LeadsState };
      const offset = params.refresh ? 0 : state.leads.offset;
      
      const response = await leadsApi.getLeads({
        ...state.leads.filters,
        ...params.filters,
        offset,
        limit: 20,
      });
      
      return { ...response, refresh: params.refresh };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch leads');
    }
  }
);

export const fetchLead = createAsyncThunk(
  'leads/fetchLead',
  async (leadId: string, { rejectWithValue }) => {
    try {
      const response = await leadsApi.getLead(leadId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch lead');
    }
  }
);

export const createVisit = createAsyncThunk(
  'leads/createVisit',
  async ({ leadId, visitData }: { leadId: string; visitData: CreateVisitRequest }, { rejectWithValue }) => {
    try {
      await leadsApi.createVisit(leadId, visitData);
      return leadId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create visit');
    }
  }
);

export const updateLeadStatus = createAsyncThunk(
  'leads/updateStatus',
  async ({ leadId, status }: { leadId: string; status: string }, { rejectWithValue }) => {
    try {
      await leadsApi.updateLeadStatus(leadId, status);
      return { leadId, status };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update lead status');
    }
  }
);

const leadsSlice = createSlice({
  name: 'leads',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<any>) => {
      state.filters = action.payload;
      state.offset = 0;
    },
    clearLeads: (state) => {
      state.leads = [];
      state.offset = 0;
      state.hasMore = true;
    },
    setCurrentLead: (state, action: PayloadAction<Lead | null>) => {
      state.currentLead = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch leads
    builder
      .addCase(fetchLeads.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLeads.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.refresh) {
          state.leads = action.payload.data;
        } else {
          state.leads.push(...action.payload.data);
        }
        state.hasMore = action.payload.hasMore;
        state.offset = action.payload.offset + action.payload.data.length;
        state.error = null;
      })
      .addCase(fetchLeads.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single lead
    builder
      .addCase(fetchLead.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLead.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentLead = action.payload;
        state.error = null;
      })
      .addCase(fetchLead.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create visit
    builder
      .addCase(createVisit.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createVisit.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update the lead in the list if it exists
        const leadIndex = state.leads.findIndex(lead => lead.id === action.payload);
        if (leadIndex !== -1) {
          // In a real app, you might want to refetch the lead or update specific fields
          state.leads[leadIndex].assignment.visitCount += 1;
        }
        state.error = null;
      })
      .addCase(createVisit.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update lead status
    builder
      .addCase(updateLeadStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateLeadStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        const { leadId, status } = action.payload;
        
        // Update the lead in the list
        const leadIndex = state.leads.findIndex(lead => lead.id === leadId);
        if (leadIndex !== -1) {
          state.leads[leadIndex].status = status as any;
        }
        
        // Update current lead if it matches
        if (state.currentLead && state.currentLead.id === leadId) {
          state.currentLead.status = status as any;
        }
        
        state.error = null;
      })
      .addCase(updateLeadStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setFilters, clearLeads, setCurrentLead } = leadsSlice.actions;

// Selectors
export const selectLeads = (state: { leads: LeadsState }) => state.leads.leads;
export const selectCurrentLead = (state: { leads: LeadsState }) => state.leads.currentLead;
export const selectLeadsLoading = (state: { leads: LeadsState }) => state.leads.isLoading;
export const selectLeadsError = (state: { leads: LeadsState }) => state.leads.error;
export const selectHasMoreLeads = (state: { leads: LeadsState }) => state.leads.hasMore;
export const selectLeadsFilters = (state: { leads: LeadsState }) => state.leads.filters;

export default leadsSlice.reducer;
