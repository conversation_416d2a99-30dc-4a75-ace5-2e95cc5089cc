import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { OfflineData, SyncQueueItem } from '@/types';

interface OfflineState {
  isOnline: boolean;
  offlineData: OfflineData;
  syncQueue: SyncQueueItem[];
  lastSync: string | null;
  isSyncing: boolean;
  syncError: string | null;
}

const initialState: OfflineState = {
  isOnline: navigator.onLine,
  offlineData: {
    leads: [],
    visits: [],
    payments: [],
    locations: [],
    lastSync: new Date().toISOString(),
  },
  syncQueue: [],
  lastSync: localStorage.getItem('lastSync'),
  isSyncing: false,
  syncError: null,
};

const offlineSlice = createSlice({
  name: 'offline',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    
    addToSyncQueue: (state, action: PayloadAction<Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>>) => {
      const item: SyncQueueItem = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        retryCount: 0,
      };
      state.syncQueue.push(item);
    },
    
    removeFromSyncQueue: (state, action: PayloadAction<string>) => {
      state.syncQueue = state.syncQueue.filter(item => item.id !== action.payload);
    },
    
    incrementRetryCount: (state, action: PayloadAction<string>) => {
      const item = state.syncQueue.find(item => item.id === action.payload);
      if (item) {
        item.retryCount += 1;
      }
    },
    
    updateOfflineData: (state, action: PayloadAction<Partial<OfflineData>>) => {
      state.offlineData = { ...state.offlineData, ...action.payload };
    },
    
    setSyncStatus: (state, action: PayloadAction<{ isSyncing: boolean; error?: string | null }>) => {
      state.isSyncing = action.payload.isSyncing;
      if (action.payload.error !== undefined) {
        state.syncError = action.payload.error;
      }
    },
    
    setLastSync: (state, action: PayloadAction<string>) => {
      state.lastSync = action.payload;
      localStorage.setItem('lastSync', action.payload);
    },
    
    clearSyncQueue: (state) => {
      state.syncQueue = [];
    },
    
    clearSyncError: (state) => {
      state.syncError = null;
    },
  },
});

export const {
  setOnlineStatus,
  addToSyncQueue,
  removeFromSyncQueue,
  incrementRetryCount,
  updateOfflineData,
  setSyncStatus,
  setLastSync,
  clearSyncQueue,
  clearSyncError,
} = offlineSlice.actions;

// Selectors
export const selectIsOnline = (state: { offline: OfflineState }) => state.offline.isOnline;
export const selectOfflineData = (state: { offline: OfflineState }) => state.offline.offlineData;
export const selectSyncQueue = (state: { offline: OfflineState }) => state.offline.syncQueue;
export const selectLastSync = (state: { offline: OfflineState }) => state.offline.lastSync;
export const selectIsSyncing = (state: { offline: OfflineState }) => state.offline.isSyncing;
export const selectSyncError = (state: { offline: OfflineState }) => state.offline.syncError;

export default offlineSlice.reducer;
