// Authentication Types
export interface LoginRequest {
  phone: string;
  password: string;
  deviceId?: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  agent: Agent;
}

export interface Agent {
  id: string;
  name: string;
  phone: string;
  email?: string;
  employeeId: string;
  role: string;
  status: string;
  currentLocation?: Location;
  performance?: Performance;
  lastActiveAt?: string;
}

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: string;
}

export interface Performance {
  collectionsToday: number;
  amountCollectedToday: number;
  successRate: number;
  avgCollectionTime: number;
  totalLeads: number;
  completedLeads: number;
}

// Lead Types
export interface Lead {
  id: string;
  customer: Customer;
  loan: Loan;
  assignment: Assignment;
  status: LeadStatus;
  visitHistory: Visit[];
  paymentHistory: Payment[];
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address: Address;
}

export interface Address {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  coordinates?: Location;
}

export interface Loan {
  id: string;
  accountNumber: string;
  principalAmount: number;
  outstandingAmount: number;
  overdueAmount: number;
  nextDueDate: string;
  overdueDays: number;
  interestRate: number;
  emiAmount: number;
}

export interface Assignment {
  assignedAt?: string;
  priority: LeadPriority;
  expectedCollectionAmount?: number;
  deadline?: string;
  visitCount: number;
  successfulVisits: number;
  totalCollected: number;
}

export interface Visit {
  id: string;
  visitDate: string;
  result: VisitResult;
  notes?: string;
  location?: Location;
  durationMinutes?: number;
  nextVisitScheduled?: string;
  followUpNotes?: string;
}

export interface Payment {
  id: string;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  paymentTimestamp: string;
  transactionId?: string;
  receiptNumber?: string;
  receiptUrl?: string;
  paymentLocation?: Location;
}

// Enums
export enum LeadStatus {
  Assigned = 'Assigned',
  Visited = 'Visited',
  Collected = 'Collected',
  Failed = 'Failed',
  Escalated = 'Escalated'
}

export enum LeadPriority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Urgent = 'Urgent'
}

export enum VisitResult {
  PaymentCollected = 'PaymentCollected',
  PartialPayment = 'PartialPayment',
  CustomerNotAvailable = 'CustomerNotAvailable',
  RefusedToPay = 'RefusedToPay',
  Rescheduled = 'Rescheduled'
}

export enum PaymentMethod {
  Cash = 'Cash',
  UPI = 'UPI',
  Card = 'Card',
  BankTransfer = 'BankTransfer',
  Cheque = 'Cheque'
}

export enum PaymentStatus {
  Pending = 'Pending',
  Success = 'Success',
  Failed = 'Failed',
  Cancelled = 'Cancelled'
}

// Request/Response Types
export interface CreateVisitRequest {
  timestamp: string;
  location?: Location;
  result: VisitResult;
  notes?: string;
  nextVisitScheduled?: string;
  followUpNotes?: string;
  photos?: string[];
}

export interface CollectPaymentRequest {
  amount: number;
  method: PaymentMethod;
  transactionDetails?: PaymentDetails;
  location?: Location;
  timestamp: string;
  receiptData?: ReceiptData;
}

export interface PaymentDetails {
  upiId?: string;
  transactionId?: string;
  gateway?: string;
  cardLastFour?: string;
  cardType?: string;
}

export interface ReceiptData {
  customerSignature?: string;
  photos?: string[];
}

export interface UpdateLocationRequest {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: string;
}

// Dashboard Types
export interface DashboardData {
  stats: DashboardStats;
  todaysLeads: Lead[];
  recentPayments: Payment[];
  notifications: Notification[];
}

export interface DashboardStats {
  pendingLeads: number;
  visitedLeads: number;
  collectedLeads: number;
  todaysVisits: number;
  todaysPayments: number;
  todaysCollection: number;
  targetAmount: number;
  achievementRate: number;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  sentAt: string;
  isRead: boolean;
  actionUrl?: string;
}

export enum NotificationType {
  Info = 'Info',
  Warning = 'Warning',
  Error = 'Error',
  Success = 'Success'
}

export enum NotificationPriority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High'
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  hasMore: boolean;
  offset: number;
  limit: number;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  statusCode: number;
}

// Offline Storage Types
export interface OfflineData {
  leads: Lead[];
  visits: Visit[];
  payments: Payment[];
  locations: Location[];
  lastSync: string;
}

export interface SyncQueueItem {
  id: string;
  type: 'visit' | 'payment' | 'location';
  data: any;
  timestamp: string;
  retryCount: number;
}

// Form Types
export interface LoginFormData {
  phone: string;
  password: string;
  rememberMe: boolean;
}

export interface VisitFormData {
  result: VisitResult;
  notes: string;
  nextVisitDate?: string;
  followUpNotes: string;
  photos: File[];
}

export interface PaymentFormData {
  amount: number;
  method: PaymentMethod;
  upiId?: string;
  transactionId?: string;
  customerSignature?: string;
  photos: File[];
}

// Component Props Types
export interface LeadCardProps {
  lead: Lead;
  onVisit: (leadId: string) => void;
  onCollect: (leadId: string) => void;
  onNavigate: (leadId: string) => void;
}

export interface PaymentModalProps {
  open: boolean;
  lead: Lead;
  onClose: () => void;
  onSubmit: (data: CollectPaymentRequest) => Promise<void>;
}

export interface VisitModalProps {
  open: boolean;
  lead: Lead;
  onClose: () => void;
  onSubmit: (data: CreateVisitRequest) => Promise<void>;
}

// Map Types
export interface MapLocation {
  latitude: number;
  longitude: number;
  title: string;
  description?: string;
  type: 'agent' | 'customer' | 'visit';
}

// Analytics Types
export interface AnalyticsData {
  dailyCollections: DailyCollection[];
  paymentMethodBreakdown: PaymentMethodStats[];
  performanceMetrics: PerformanceMetrics;
  territoryStats: TerritoryStats[];
}

export interface DailyCollection {
  date: string;
  amount: number;
  count: number;
}

export interface PaymentMethodStats {
  method: PaymentMethod;
  count: number;
  amount: number;
  percentage: number;
}

export interface PerformanceMetrics {
  totalLeads: number;
  completedLeads: number;
  successRate: number;
  avgCollectionTime: number;
  totalAmount: number;
}

export interface TerritoryStats {
  area: string;
  leadsCount: number;
  collectionsCount: number;
  totalAmount: number;
}
