import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  LoginRequest,
  LoginResponse,
  Agent,
  Lead,
  CreateVisitRequest,
  CollectPaymentRequest,
  UpdateLocationRequest,
  DashboardData,
  PaginatedResponse,
} from '@/types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post('/api/auth/refresh', {
            refreshToken,
          });

          const { accessToken } = response.data;
          localStorage.setItem('accessToken', accessToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },

  refreshToken: async (refreshToken: string): Promise<{ accessToken: string; expiresIn: number }> => {
    const response = await api.post('/auth/refresh', { refreshToken });
    return response.data;
  },

  updateProfile: async (profileData: Partial<Agent>): Promise<Agent> => {
    const response = await api.put<Agent>('/agents/profile', profileData);
    return response.data;
  },
};

export const agentsApi = {
  getProfile: async (): Promise<Agent> => {
    const response = await api.get<Agent>('/agents/profile');
    return response.data;
  },

  updateLocation: async (locationData: UpdateLocationRequest): Promise<void> => {
    await api.post('/agents/location', locationData);
  },

  getDashboard: async (date?: string): Promise<DashboardData> => {
    const params = date ? { date } : {};
    const response = await api.get<DashboardData>('/agents/dashboard', { params });
    return response.data;
  },
};

export const leadsApi = {
  getLeads: async (params?: {
    status?: string;
    date?: string;
    limit?: number;
    offset?: number;
  }): Promise<PaginatedResponse<Lead>> => {
    const response = await api.get<Lead[]>('/leads', { params });
    
    // Extract pagination info from headers
    const total = parseInt(response.headers['x-total-count'] || '0');
    const hasMore = response.headers['x-has-more'] === 'true';
    
    return {
      data: response.data,
      total,
      hasMore,
      offset: params?.offset || 0,
      limit: params?.limit || 50,
    };
  },

  getLead: async (id: string): Promise<Lead> => {
    const response = await api.get<Lead>(`/leads/${id}`);
    return response.data;
  },

  createVisit: async (leadId: string, visitData: CreateVisitRequest): Promise<void> => {
    await api.post(`/leads/${leadId}/visit`, visitData);
  },

  updateLeadStatus: async (leadId: string, status: string): Promise<void> => {
    await api.put(`/leads/${leadId}/status`, { status });
  },
};

export const paymentsApi = {
  collectPayment: async (leadId: string, paymentData: CollectPaymentRequest): Promise<void> => {
    await api.post(`/payments/collect`, { ...paymentData, leadId });
  },

  getPaymentHistory: async (params?: {
    agentId?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<any> => {
    const response = await api.get('/payments/history', { params });
    return response.data;
  },

  downloadReceipt: async (paymentId: string): Promise<Blob> => {
    const response = await api.get(`/payments/${paymentId}/receipt`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

export const analyticsApi = {
  getDashboard: async (period?: string, agentId?: string): Promise<any> => {
    const params = { period, agentId };
    const response = await api.get('/analytics/dashboard', { params });
    return response.data;
  },

  getPerformance: async (agentId?: string, dateFrom?: string, dateTo?: string): Promise<any> => {
    const params = { agentId, dateFrom, dateTo };
    const response = await api.get('/analytics/performance', { params });
    return response.data;
  },

  generateReport: async (reportType: string, params?: any): Promise<Blob> => {
    const response = await api.post(`/analytics/reports/${reportType}`, params, {
      responseType: 'blob',
    });
    return response.data;
  },
};

export const syncApi = {
  uploadData: async (syncData: any): Promise<any> => {
    const response = await api.post('/sync/upload', syncData);
    return response.data;
  },

  downloadData: async (since?: string): Promise<any> => {
    const params = since ? { since } : {};
    const response = await api.get('/sync/download', { params });
    return response.data;
  },
};

// Utility functions
export const uploadFile = async (file: File, path: string): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post(`/upload/${path}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data.url;
};

export const downloadFile = async (url: string, filename: string): Promise<void> => {
  const response = await api.get(url, {
    responseType: 'blob',
  });

  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(downloadUrl);
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.response) {
    // Server responded with error status
    return error.response.data?.message || `Error: ${error.response.status}`;
  } else if (error.request) {
    // Request was made but no response received
    return 'Network error. Please check your connection.';
  } else {
    // Something else happened
    return error.message || 'An unexpected error occurred.';
  }
};

export default api;
