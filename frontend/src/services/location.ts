// Location Tracking Service
import { UpdateLocationRequest } from '@/types';
import { agentsApi } from './api';

interface LocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}

interface LocationState {
  isTracking: boolean;
  watchId: number | null;
  lastLocation: GeolocationPosition | null;
  error: string | null;
}

class LocationService {
  private state: LocationState = {
    isTracking: false,
    watchId: null,
    lastLocation: null,
    error: null,
  };

  private options: LocationOptions = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 60000, // 1 minute
  };

  private updateInterval: number = 5 * 60 * 1000; // 5 minutes
  private lastUpdateTime: number = 0;

  async getCurrentLocation(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.state.lastLocation = position;
          this.state.error = null;
          resolve(position);
        },
        (error) => {
          this.state.error = this.getErrorMessage(error);
          reject(new Error(this.state.error));
        },
        this.options
      );
    });
  }

  startTracking(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      if (this.state.isTracking) {
        resolve();
        return;
      }

      this.state.watchId = navigator.geolocation.watchPosition(
        (position) => {
          this.state.lastLocation = position;
          this.state.error = null;
          this.handleLocationUpdate(position);
        },
        (error) => {
          this.state.error = this.getErrorMessage(error);
          console.error('Location tracking error:', this.state.error);
        },
        this.options
      );

      this.state.isTracking = true;
      console.log('Location tracking started');
      resolve();
    });
  }

  stopTracking(): void {
    if (this.state.watchId !== null) {
      navigator.geolocation.clearWatch(this.state.watchId);
      this.state.watchId = null;
    }
    this.state.isTracking = false;
    console.log('Location tracking stopped');
  }

  private async handleLocationUpdate(position: GeolocationPosition): Promise<void> {
    const now = Date.now();
    
    // Only update if enough time has passed since last update
    if (now - this.lastUpdateTime < this.updateInterval) {
      return;
    }

    try {
      const locationData: UpdateLocationRequest = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: new Date().toISOString(),
      };

      await agentsApi.updateLocation(locationData);
      this.lastUpdateTime = now;
      console.log('Location updated successfully');
    } catch (error) {
      console.error('Failed to update location:', error);
    }
  }

  private getErrorMessage(error: GeolocationPositionError): string {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        return 'Location access denied by user';
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable';
      case error.TIMEOUT:
        return 'Location request timed out';
      default:
        return 'An unknown error occurred while retrieving location';
    }
  }

  getState(): LocationState {
    return { ...this.state };
  }

  isSupported(): boolean {
    return 'geolocation' in navigator;
  }

  async requestPermission(): Promise<PermissionState> {
    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        return permission.state;
      } catch (error) {
        console.error('Failed to query geolocation permission:', error);
      }
    }
    
    // Fallback: try to get current location to trigger permission prompt
    try {
      await this.getCurrentLocation();
      return 'granted';
    } catch (error) {
      return 'denied';
    }
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  formatCoordinates(lat: number, lon: number): string {
    const latDir = lat >= 0 ? 'N' : 'S';
    const lonDir = lon >= 0 ? 'E' : 'W';
    return `${Math.abs(lat).toFixed(6)}°${latDir}, ${Math.abs(lon).toFixed(6)}°${lonDir}`;
  }

  async getAddressFromCoordinates(lat: number, lon: number): Promise<string> {
    try {
      // This would typically use a geocoding service like Google Maps
      // For now, return formatted coordinates
      return this.formatCoordinates(lat, lon);
    } catch (error) {
      console.error('Failed to get address from coordinates:', error);
      return this.formatCoordinates(lat, lon);
    }
  }
}

// Create singleton instance
const locationService = new LocationService();

// Export convenience functions
export const startLocationTracking = (): Promise<void> => {
  return locationService.startTracking();
};

export const stopLocationTracking = (): void => {
  locationService.stopTracking();
};

export const getCurrentLocation = (): Promise<GeolocationPosition> => {
  return locationService.getCurrentLocation();
};

export const requestLocationPermission = (): Promise<PermissionState> => {
  return locationService.requestPermission();
};

export const isLocationSupported = (): boolean => {
  return locationService.isSupported();
};

export const getLocationState = (): LocationState => {
  return locationService.getState();
};

export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  return locationService.calculateDistance(lat1, lon1, lat2, lon2);
};

export const formatCoordinates = (lat: number, lon: number): string => {
  return locationService.formatCoordinates(lat, lon);
};

export const getAddressFromCoordinates = (lat: number, lon: number): Promise<string> => {
  return locationService.getAddressFromCoordinates(lat, lon);
};

export default locationService;
