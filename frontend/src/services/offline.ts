// Offline Storage and Synchronization Service
import localforage from 'localforage';
import { OfflineData, SyncQueueItem } from '@/types';

// Configure localforage
localforage.config({
  name: 'FieldEZ',
  version: 1.0,
  storeName: 'fieldez_data',
  description: 'FieldEZ offline data storage'
});

const STORAGE_KEYS = {
  OFFLINE_DATA: 'offline_data',
  SYNC_QUEUE: 'sync_queue',
  LAST_SYNC: 'last_sync',
  USER_PREFERENCES: 'user_preferences',
};

export const initializeOfflineStorage = async (): Promise<void> => {
  try {
    // Initialize offline data structure if not exists
    const existingData = await localforage.getItem<OfflineData>(STORAGE_KEYS.OFFLINE_DATA);
    if (!existingData) {
      const initialData: OfflineData = {
        leads: [],
        visits: [],
        payments: [],
        locations: [],
        lastSync: new Date().toISOString(),
      };
      await localforage.setItem(STORAGE_KEYS.OFFLINE_DATA, initialData);
    }

    // Initialize sync queue if not exists
    const existingQueue = await localforage.getItem<SyncQueueItem[]>(STORAGE_KEYS.SYNC_QUEUE);
    if (!existingQueue) {
      await localforage.setItem(STORAGE_KEYS.SYNC_QUEUE, []);
    }

    console.log('Offline storage initialized successfully');
  } catch (error) {
    console.error('Failed to initialize offline storage:', error);
    throw error;
  }
};

export const getOfflineData = async (): Promise<OfflineData | null> => {
  try {
    return await localforage.getItem<OfflineData>(STORAGE_KEYS.OFFLINE_DATA);
  } catch (error) {
    console.error('Failed to get offline data:', error);
    return null;
  }
};

export const saveOfflineData = async (data: Partial<OfflineData>): Promise<void> => {
  try {
    const existingData = await getOfflineData();
    const updatedData = { ...existingData, ...data };
    await localforage.setItem(STORAGE_KEYS.OFFLINE_DATA, updatedData);
  } catch (error) {
    console.error('Failed to save offline data:', error);
    throw error;
  }
};

export const getSyncQueue = async (): Promise<SyncQueueItem[]> => {
  try {
    return (await localforage.getItem<SyncQueueItem[]>(STORAGE_KEYS.SYNC_QUEUE)) || [];
  } catch (error) {
    console.error('Failed to get sync queue:', error);
    return [];
  }
};

export const addToSyncQueue = async (item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>): Promise<void> => {
  try {
    const queue = await getSyncQueue();
    const newItem: SyncQueueItem = {
      ...item,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      retryCount: 0,
    };
    queue.push(newItem);
    await localforage.setItem(STORAGE_KEYS.SYNC_QUEUE, queue);
  } catch (error) {
    console.error('Failed to add item to sync queue:', error);
    throw error;
  }
};

export const removeFromSyncQueue = async (itemId: string): Promise<void> => {
  try {
    const queue = await getSyncQueue();
    const updatedQueue = queue.filter(item => item.id !== itemId);
    await localforage.setItem(STORAGE_KEYS.SYNC_QUEUE, updatedQueue);
  } catch (error) {
    console.error('Failed to remove item from sync queue:', error);
    throw error;
  }
};

export const clearSyncQueue = async (): Promise<void> => {
  try {
    await localforage.setItem(STORAGE_KEYS.SYNC_QUEUE, []);
  } catch (error) {
    console.error('Failed to clear sync queue:', error);
    throw error;
  }
};

export const getLastSyncTime = async (): Promise<string | null> => {
  try {
    return await localforage.getItem<string>(STORAGE_KEYS.LAST_SYNC);
  } catch (error) {
    console.error('Failed to get last sync time:', error);
    return null;
  }
};

export const setLastSyncTime = async (timestamp: string): Promise<void> => {
  try {
    await localforage.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
  } catch (error) {
    console.error('Failed to set last sync time:', error);
    throw error;
  }
};

export const clearOfflineData = async (): Promise<void> => {
  try {
    await localforage.clear();
    await initializeOfflineStorage();
  } catch (error) {
    console.error('Failed to clear offline data:', error);
    throw error;
  }
};

export const getStorageUsage = async (): Promise<{ used: number; quota: number }> => {
  try {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0,
      };
    }
    return { used: 0, quota: 0 };
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    return { used: 0, quota: 0 };
  }
};

// Utility functions for offline detection
export const isOnline = (): boolean => {
  return navigator.onLine;
};

export const setupOfflineListeners = (
  onOnline: () => void,
  onOffline: () => void
): (() => void) => {
  const handleOnline = () => {
    console.log('App is online');
    onOnline();
  };

  const handleOffline = () => {
    console.log('App is offline');
    onOffline();
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};
