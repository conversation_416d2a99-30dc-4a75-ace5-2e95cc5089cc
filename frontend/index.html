<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="FieldEZ Loan Collection - Professional loan collection management system for field agents" />
    <meta name="theme-color" content="#1976d2" />
    
    <!-- PWA Meta Tags -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Preconnect to improve performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Material-UI Roboto Font -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap"
    />
    
    <!-- Material-UI Icons -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />
    
    <title>FieldEZ - Loan Collection Management</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Roboto', sans-serif;
      }
      
      .loading-logo {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
        text-align: center;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }
      
      .loading-text {
        font-size: 1.1rem;
        opacity: 0.9;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        display: none;
      }
      
      /* Reset default styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Roboto', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
      }
      
      #root {
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        FieldEZ
      </div>
      <div class="loading-spinner"></div>
      <div class="loading-text">
        Loading Loan Collection System...
      </div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- Hide loading screen when React app mounts -->
    <script>
      // Hide loading screen when the app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
