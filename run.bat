@echo off
setlocal enabledelayedexpansion

REM FieldEZ Loan Collection System - Quick Start Script for Windows
REM This script helps you quickly set up and run the application

echo.
echo 🚀 FieldEZ Loan Collection System - Quick Start
echo ================================================

set "command=%~1"
if "%command%"=="" set "command=setup"

goto :%command% 2>nul || goto :help

:setup
echo [INFO] Setting up FieldEZ Loan Collection System...
call :check_prerequisites
if errorlevel 1 exit /b 1

call :setup_database
call :setup_backend
if errorlevel 1 exit /b 1

call :setup_frontend
if errorlevel 1 exit /b 1

echo.
echo [SUCCESS] Setup completed! Run 'run.bat start' to start the application
goto :eof

:start
echo [INFO] Starting FieldEZ Loan Collection System...
echo.
echo [INFO] Starting backend API...
start "FieldEZ Backend" cmd /c "cd backend\FieldEZ.API && dotnet run"

echo [INFO] Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo [INFO] Starting frontend...
start "FieldEZ Frontend" cmd /c "cd frontend && npm run dev"

echo.
echo [SUCCESS] Application started successfully!
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:5000
echo 📚 API Documentation: http://localhost:5000/swagger
echo.
echo 📱 Demo Login Credentials:
echo    Phone: +91 9876543210
echo    Password: demo123
echo.
echo Press any key to stop the application...
pause >nul

echo [INFO] Stopping application...
taskkill /f /im "dotnet.exe" 2>nul
taskkill /f /im "node.exe" 2>nul
goto :eof

:backend
echo [INFO] Starting backend only...
cd backend\FieldEZ.API
dotnet run
goto :eof

:frontend
echo [INFO] Starting frontend only...
cd frontend
npm run dev
goto :eof

:check_prerequisites
echo [INFO] Checking prerequisites...

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] .NET 8 SDK is not installed. Please install it from https://dotnet.microsoft.com/download
    exit /b 1
)

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install it from https://nodejs.org
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install Node.js which includes npm
    exit /b 1
)

echo [SUCCESS] All prerequisites are installed
goto :eof

:setup_database
echo [INFO] Setting up database...

REM Check if sqlcmd is available
sqlcmd -? >nul 2>&1
if errorlevel 1 (
    echo [WARNING] sqlcmd not found. Please set up the database manually using SQL Server Management Studio
    echo [WARNING] 1. Create database 'FieldEZ'
    echo [WARNING] 2. Run scripts in database\scripts\ folder in order
    goto :eof
)

echo [INFO] Creating database and running scripts...

REM Create database
sqlcmd -S localhost -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'FieldEZ') CREATE DATABASE FieldEZ;" 2>nul
if errorlevel 1 (
    echo [WARNING] Could not create database automatically. Please create 'FieldEZ' database manually in SQL Server
)

REM Run database scripts if they exist
if exist "database\scripts\01-create-tables.sql" (
    echo [INFO] Running table creation script...
    sqlcmd -S localhost -d FieldEZ -i database\scripts\01-create-tables.sql 2>nul
    if errorlevel 1 echo [WARNING] Could not run table creation script automatically
)

if exist "database\scripts\02-create-indexes.sql" (
    echo [INFO] Running indexes script...
    sqlcmd -S localhost -d FieldEZ -i database\scripts\02-create-indexes.sql 2>nul
    if errorlevel 1 echo [WARNING] Could not run indexes script automatically
)

if exist "database\scripts\03-seed-data.sql" (
    echo [INFO] Running seed data script...
    sqlcmd -S localhost -d FieldEZ -i database\scripts\03-seed-data.sql 2>nul
    if errorlevel 1 echo [WARNING] Could not run seed data script automatically
)

echo [SUCCESS] Database setup completed
goto :eof

:setup_backend
echo [INFO] Setting up backend...

if not exist "backend" (
    echo [ERROR] Backend directory not found
    exit /b 1
)

cd backend

echo [INFO] Restoring NuGet packages...
dotnet restore
if errorlevel 1 (
    echo [ERROR] Failed to restore NuGet packages
    cd ..
    exit /b 1
)

echo [INFO] Building backend...
dotnet build
if errorlevel 1 (
    echo [ERROR] Failed to build backend
    cd ..
    exit /b 1
)

cd ..
echo [SUCCESS] Backend setup completed
goto :eof

:setup_frontend
echo [INFO] Setting up frontend...

if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    exit /b 1
)

cd frontend

echo [INFO] Installing npm packages...
npm install
if errorlevel 1 (
    echo [ERROR] Failed to install npm packages
    cd ..
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating .env file...
    (
        echo VITE_API_BASE_URL=http://localhost:5000/api
        echo VITE_APP_NAME=FieldEZ Loan Collection
        echo VITE_APP_VERSION=1.0.0
    ) > .env
)

cd ..
echo [SUCCESS] Frontend setup completed
goto :eof

:help
echo Usage: run.bat [command]
echo.
echo Commands:
echo   setup     - Set up the application (default)
echo   start     - Start both backend and frontend
echo   backend   - Start backend only
echo   frontend  - Start frontend only
echo   help      - Show this help message
goto :eof

:error
echo [ERROR] Unknown command: %command%
echo Run 'run.bat help' for usage information
exit /b 1
