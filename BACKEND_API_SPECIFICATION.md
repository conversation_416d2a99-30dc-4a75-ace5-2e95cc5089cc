# Backend API Specification

## Overview
RESTful API backend for the loan collection system with microservices architecture, supporting agent management, lead distribution, payment processing, and real-time analytics.

## API Architecture

### Base Configuration
```yaml
# API Configuration
base_url: https://api.loancollection.com/v1
authentication: Bearer JWT tokens
rate_limiting: 1000 requests/hour per agent
response_format: JSON
error_format: RFC 7807 Problem Details
```

### Authentication Endpoints

#### POST /auth/login
```json
// Request
{
  "phone": "+91-9876543210",
  "password": "hashed_password",
  "device_id": "device_unique_id"
}

// Response
{
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "expires_in": 3600,
  "agent": {
    "id": "agent_123",
    "name": "<PERSON> Doe",
    "phone": "+91-9876543210",
    "role": "field_agent",
    "permissions": ["collect_payment", "view_leads"]
  }
}
```

#### POST /auth/refresh
```json
// Request
{
  "refresh_token": "jwt_refresh_token"
}

// Response
{
  "access_token": "new_jwt_access_token",
  "expires_in": 3600
}
```

### Agent Management Endpoints

#### GET /agents/profile
```json
// Response
{
  "id": "agent_123",
  "name": "John Doe",
  "phone": "+91-9876543210",
  "email": "<EMAIL>",
  "employee_id": "EMP001",
  "status": "active",
  "location": {
    "lat": 12.9716,
    "lng": 77.5946,
    "address": "Bangalore, Karnataka"
  },
  "performance": {
    "collections_today": 5,
    "amount_collected_today": 125000,
    "success_rate": 85.5,
    "avg_collection_time": 45
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### PUT /agents/location
```json
// Request
{
  "lat": 12.9716,
  "lng": 77.5946,
  "timestamp": "2024-01-15T10:30:00Z"
}

// Response
{
  "status": "success",
  "message": "Location updated successfully"
}
```

### Lead Management Endpoints

#### GET /leads
```json
// Query Parameters: ?status=assigned&date=2024-01-15&limit=50&offset=0

// Response
{
  "leads": [
    {
      "id": "lead_123",
      "customer": {
        "name": "Jane Smith",
        "phone": "+91-**********",
        "email": "<EMAIL>",
        "address": {
          "street": "123 Main St",
          "city": "Bangalore",
          "state": "Karnataka",
          "pincode": "560001",
          "coordinates": {
            "lat": 12.9716,
            "lng": 77.5946
          }
        }
      },
      "loan": {
        "account_number": "LOAN123456",
        "principal_amount": 100000,
        "outstanding_amount": 50000,
        "due_date": "2024-01-15",
        "overdue_days": 5,
        "interest_rate": 12.5
      },
      "assignment": {
        "assigned_at": "2024-01-15T08:00:00Z",
        "priority": "high",
        "expected_collection": 25000,
        "deadline": "2024-01-15T18:00:00Z"
      },
      "status": "assigned",
      "visit_history": [
        {
          "date": "2024-01-10",
          "result": "customer_not_available",
          "notes": "House locked, neighbors said family is out of town"
        }
      ]
    }
  ],
  "pagination": {
    "total": 150,
    "limit": 50,
    "offset": 0,
    "has_more": true
  }
}
```

#### GET /leads/{lead_id}
```json
// Response - Detailed lead information
{
  "id": "lead_123",
  "customer": { /* detailed customer info */ },
  "loan": { /* detailed loan info */ },
  "assignment": { /* assignment details */ },
  "payment_history": [
    {
      "date": "2024-01-01",
      "amount": 10000,
      "method": "upi",
      "transaction_id": "txn_456",
      "receipt_url": "https://receipts.com/receipt_789.pdf"
    }
  ],
  "visit_history": [ /* all previous visits */ ],
  "documents": [
    {
      "type": "agreement",
      "url": "https://docs.com/agreement_123.pdf",
      "uploaded_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST /leads/{lead_id}/visit
```json
// Request
{
  "timestamp": "2024-01-15T10:30:00Z",
  "location": {
    "lat": 12.9716,
    "lng": 77.5946
  },
  "result": "payment_collected", // payment_collected, customer_not_available, refused_to_pay, partial_payment
  "notes": "Customer paid full amount via UPI",
  "photos": [
    "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  ]
}

// Response
{
  "visit_id": "visit_789",
  "status": "recorded",
  "next_action": "mark_as_collected" // or "schedule_follow_up"
}
```

### Payment Processing Endpoints

#### POST /payments/collect
```json
// Request
{
  "lead_id": "lead_123",
  "amount": 25000,
  "method": "upi", // cash, upi, card, bank_transfer
  "transaction_details": {
    "upi_id": "customer@paytm",
    "transaction_id": "txn_456",
    "gateway": "razorpay"
  },
  "location": {
    "lat": 12.9716,
    "lng": 77.5946
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "receipt_data": {
    "customer_signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "photos": ["data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."]
  }
}

// Response
{
  "payment_id": "payment_789",
  "status": "success",
  "receipt": {
    "id": "receipt_101",
    "url": "https://receipts.com/receipt_101.pdf",
    "download_url": "https://receipts.com/download/receipt_101.pdf"
  },
  "updated_lead_status": "partially_collected", // or "fully_collected"
  "remaining_amount": 25000
}
```

#### GET /payments/history
```json
// Query Parameters: ?agent_id=agent_123&date_from=2024-01-01&date_to=2024-01-15

// Response
{
  "payments": [
    {
      "id": "payment_789",
      "lead_id": "lead_123",
      "customer_name": "Jane Smith",
      "amount": 25000,
      "method": "upi",
      "status": "success",
      "timestamp": "2024-01-15T10:30:00Z",
      "receipt_url": "https://receipts.com/receipt_101.pdf"
    }
  ],
  "summary": {
    "total_amount": 125000,
    "total_transactions": 5,
    "success_rate": 100,
    "methods": {
      "cash": 2,
      "upi": 2,
      "card": 1
    }
  }
}
```

### Lead Distribution Endpoints

#### POST /distribution/assign
```json
// Request (Admin only)
{
  "leads": ["lead_123", "lead_124", "lead_125"],
  "criteria": {
    "proximity_weight": 0.4,
    "workload_weight": 0.3,
    "performance_weight": 0.3
  },
  "constraints": {
    "max_leads_per_agent": 20,
    "preferred_agents": ["agent_123", "agent_124"]
  }
}

// Response
{
  "assignments": [
    {
      "lead_id": "lead_123",
      "agent_id": "agent_123",
      "score": 0.85,
      "reasons": ["closest_agent", "low_workload", "high_performance"]
    }
  ],
  "summary": {
    "total_assigned": 3,
    "agents_involved": 2,
    "avg_distance": 2.5
  }
}
```

### Analytics & Reporting Endpoints

#### GET /analytics/dashboard
```json
// Query Parameters: ?period=today&agent_id=agent_123

// Response
{
  "period": "today",
  "agent_performance": {
    "collections": {
      "count": 5,
      "amount": 125000,
      "target": 150000,
      "achievement_rate": 83.33
    },
    "visits": {
      "total": 8,
      "successful": 5,
      "success_rate": 62.5
    },
    "efficiency": {
      "avg_time_per_collection": 45,
      "distance_traveled": 25.5,
      "fuel_efficiency": "good"
    }
  },
  "team_comparison": {
    "rank": 3,
    "total_agents": 15,
    "percentile": 80
  }
}
```

### Synchronization Endpoints

#### POST /sync/upload
```json
// Request - Bulk upload of offline data
{
  "device_id": "device_unique_id",
  "last_sync": "2024-01-15T08:00:00Z",
  "data": {
    "visits": [
      {
        "local_id": "local_visit_1",
        "lead_id": "lead_123",
        "timestamp": "2024-01-15T10:30:00Z",
        "result": "payment_collected",
        "notes": "Customer paid via cash"
      }
    ],
    "payments": [
      {
        "local_id": "local_payment_1",
        "lead_id": "lead_123",
        "amount": 25000,
        "method": "cash",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ],
    "locations": [
      {
        "lat": 12.9716,
        "lng": 77.5946,
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
}

// Response
{
  "sync_id": "sync_456",
  "status": "success",
  "processed": {
    "visits": 1,
    "payments": 1,
    "locations": 1
  },
  "conflicts": [],
  "server_updates": {
    "new_leads": 2,
    "updated_leads": 1,
    "system_messages": 1
  }
}
```

#### GET /sync/download
```json
// Query Parameters: ?since=2024-01-15T08:00:00Z

// Response
{
  "timestamp": "2024-01-15T12:00:00Z",
  "updates": {
    "leads": [
      {
        "id": "lead_126",
        "action": "created",
        "data": { /* full lead data */ }
      },
      {
        "id": "lead_123",
        "action": "updated",
        "data": { /* updated fields only */ }
      }
    ],
    "assignments": [
      {
        "lead_id": "lead_126",
        "agent_id": "agent_123",
        "assigned_at": "2024-01-15T11:30:00Z"
      }
    ],
    "system_messages": [
      {
        "id": "msg_789",
        "type": "announcement",
        "title": "System Maintenance",
        "message": "Scheduled maintenance tonight from 2 AM to 4 AM",
        "priority": "medium"
      }
    ]
  }
}
```

## Error Handling

### Standard Error Response
```json
{
  "type": "https://api.loancollection.com/errors/validation-error",
  "title": "Validation Error",
  "status": 400,
  "detail": "The request contains invalid data",
  "instance": "/leads/123/visit",
  "errors": [
    {
      "field": "amount",
      "code": "INVALID_AMOUNT",
      "message": "Amount must be greater than 0"
    }
  ]
}
```

### HTTP Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 409: Conflict
- 422: Unprocessable Entity
- 429: Too Many Requests
- 500: Internal Server Error

## Rate Limiting
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642262400
```

This API specification provides a comprehensive foundation for building a robust backend system that supports all the features required for an effective loan collection management platform.
