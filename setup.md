# FieldEZ Loan Collection System - Setup Guide

## Prerequisites

### Software Requirements
- **.NET 8 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
- **Node.js 18+** - Download from [Node.js](https://nodejs.org/)
- **SQL Server 2019+** - Download from [Microsoft SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
- **Visual Studio 2022** or **VS Code** - For development

### Tools (Optional but Recommended)
- **SQL Server Management Studio (SSMS)** - For database management
- **Postman** - For API testing
- **Git** - For version control

## Step 1: Clone and Setup Project Structure

```bash
# Create project directory
mkdir FieldEZ-LoanCollection
cd FieldEZ-LoanCollection

# Create backend structure
mkdir -p backend/{FieldEZ.API,FieldEZ.Core,FieldEZ.Infrastructure,FieldEZ.Application,FieldEZ.Tests}

# Create frontend structure
mkdir -p frontend/{src,public}

# Create database structure
mkdir -p database/{scripts,migrations}

# Create documentation
mkdir -p docs/{api,deployment}
```

## Step 2: Database Setup

### 2.1 Create Database
```sql
-- Connect to SQL Server and run:
CREATE DATABASE FieldEZ;
GO
```

### 2.2 Run Database Scripts
```bash
# Navigate to database scripts directory
cd database/scripts

# Run scripts in order
sqlcmd -S localhost -d FieldEZ -i 01-create-tables.sql
sqlcmd -S localhost -d FieldEZ -i 02-create-indexes.sql
sqlcmd -S localhost -d FieldEZ -i 03-seed-data.sql
```

### 2.3 Connection String
Update your connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FieldEZ;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

## Step 3: Backend Setup (.NET 8)

### 3.1 Create Projects
```bash
cd backend

# Create solution
dotnet new sln -n FieldEZ

# Create projects
dotnet new classlib -n FieldEZ.Core
dotnet new classlib -n FieldEZ.Infrastructure
dotnet new classlib -n FieldEZ.Application
dotnet new webapi -n FieldEZ.API
dotnet new xunit -n FieldEZ.Tests

# Add projects to solution
dotnet sln add FieldEZ.Core/FieldEZ.Core.csproj
dotnet sln add FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj
dotnet sln add FieldEZ.Application/FieldEZ.Application.csproj
dotnet sln add FieldEZ.API/FieldEZ.API.csproj
dotnet sln add FieldEZ.Tests/FieldEZ.Tests.csproj
```

### 3.2 Add Project References
```bash
# API references
cd FieldEZ.API
dotnet add reference ../FieldEZ.Application/FieldEZ.Application.csproj
dotnet add reference ../FieldEZ.Infrastructure/FieldEZ.Infrastructure.csproj

# Infrastructure references
cd ../FieldEZ.Infrastructure
dotnet add reference ../FieldEZ.Core/FieldEZ.Core.csproj

# Application references
cd ../FieldEZ.Application
dotnet add reference ../FieldEZ.Core/FieldEZ.Core.csproj
```

### 3.3 Install NuGet Packages

#### FieldEZ.Core
```bash
cd FieldEZ.Core
dotnet add package Microsoft.EntityFrameworkCore
dotnet add package NetTopologySuite
```

#### FieldEZ.Infrastructure
```bash
cd ../FieldEZ.Infrastructure
dotnet add package Microsoft.EntityFrameworkCore.SqlServer
dotnet add package Microsoft.EntityFrameworkCore.Tools
dotnet add package NetTopologySuite.IO.SqlServerBytes
```

#### FieldEZ.API
```bash
cd ../FieldEZ.API
dotnet add package Microsoft.EntityFrameworkCore.Design
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer
dotnet add package BCrypt.Net-Next
dotnet add package Swashbuckle.AspNetCore
dotnet add package Microsoft.AspNetCore.SignalR
dotnet add package Serilog.AspNetCore
```

### 3.4 Configure Startup
Create `Program.cs` in FieldEZ.API:
```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using FieldEZ.Infrastructure.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database
builder.Services.AddDbContext<FieldEZDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
        x => x.UseNetTopologySuite()));

// JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.ASCII.GetBytes(builder.Configuration["Jwt:Secret"])),
            ValidateIssuer = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidateAudience = true,
            ValidAudience = builder.Configuration["Jwt:Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });

// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins("http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowFrontend");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
```

### 3.5 Run Backend
```bash
cd FieldEZ.API
dotnet run
```

## Step 4: Frontend Setup (React.js)

### 4.1 Initialize React Project
```bash
cd frontend

# Install dependencies
npm install

# Install additional dependencies for development
npm install -D @types/node
```

### 4.2 Environment Configuration
Create `.env` file:
```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_APP_NAME=FieldEZ Loan Collection
VITE_APP_VERSION=1.0.0
```

### 4.3 Create Required Directories
```bash
mkdir -p src/{components,pages,services,store,hooks,utils,types}
mkdir -p src/store/slices
mkdir -p public/icons
```

### 4.4 Run Frontend
```bash
npm run dev
```

## Step 5: Database Seeding (Optional)

Create `03-seed-data.sql`:
```sql
-- Insert demo agent
INSERT INTO Agents (EmployeeId, Name, Phone, Email, PasswordHash, Status, Role)
VALUES ('EMP001', 'Demo Agent', '+************', '<EMAIL>', 
        '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
        'Active', 'FieldAgent');

-- Insert demo customer
INSERT INTO Customers (Name, Phone, AddressLine1, City, State, Pincode, Country)
VALUES ('John Doe', '+************', '123 Main Street', 'Mumbai', 'Maharashtra', '400001', 'India');

-- Insert demo loan
DECLARE @CustomerId UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Customers);
INSERT INTO Loans (AccountNumber, CustomerId, PrincipalAmount, InterestRate, TenureMonths, 
                   EMIAmount, OutstandingAmount, OverdueAmount, NextDueDate, OverdueDays, 
                   DisbursedAt, MaturityDate)
VALUES ('LOAN001', @CustomerId, 100000, 12.5, 24, 4500, 50000, 10000, 
        DATEADD(day, -5, GETDATE()), 5, DATEADD(month, -12, GETDATE()), 
        DATEADD(month, 12, GETDATE()));

-- Insert demo lead
DECLARE @AgentId UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Agents);
DECLARE @LoanId UNIQUEIDENTIFIER = (SELECT TOP 1 Id FROM Loans);
INSERT INTO Leads (LoanId, CustomerId, AssignedAgentId, AssignedAt, Priority, 
                   ExpectedCollectionAmount, Deadline, Status)
VALUES (@LoanId, @CustomerId, @AgentId, GETDATE(), 'High', 25000, 
        DATEADD(day, 7, GETDATE()), 'Assigned');
```

## Step 6: Testing the Application

### 6.1 Backend Testing
```bash
# Test API endpoints
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone": "+************", "password": "demo123"}'
```

### 6.2 Frontend Testing
1. Open browser to `http://localhost:3000`
2. Login with demo credentials:
   - Phone: +91 9876543210
   - Password: demo123

## Step 7: Production Deployment

### 7.1 Backend Deployment
```bash
# Publish backend
cd backend/FieldEZ.API
dotnet publish -c Release -o ./publish

# Deploy to IIS or cloud service
```

### 7.2 Frontend Deployment
```bash
# Build frontend
cd frontend
npm run build

# Deploy build folder to web server
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify SQL Server is running
   - Check connection string
   - Ensure database exists

2. **CORS Issues**
   - Verify CORS policy in backend
   - Check frontend API base URL

3. **JWT Token Issues**
   - Verify JWT secret configuration
   - Check token expiration

4. **PWA Issues**
   - Ensure HTTPS in production
   - Check service worker registration

### Logs and Debugging

- Backend logs: Check console output or configure Serilog
- Frontend logs: Check browser developer tools
- Database logs: Check SQL Server logs

## Next Steps

1. Configure production environment variables
2. Set up CI/CD pipeline
3. Configure monitoring and logging
4. Set up backup strategies
5. Implement additional security measures

## Support

For issues and questions:
- Check the documentation in `/docs`
- Review the API documentation at `/swagger`
- Contact the development team
