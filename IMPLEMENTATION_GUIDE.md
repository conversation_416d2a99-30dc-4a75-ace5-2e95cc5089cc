# FieldEZ-Inspired Loan Collection System - Implementation Guide

## Project Overview

This guide provides step-by-step instructions for implementing a comprehensive loan collection management system similar to FieldEZ, featuring intelligent agent distribution, mobile PWA, and robust backend infrastructure.

## Technology Stack

### Frontend
- **Mobile PWA**: React 18 + TypeScript + Workbox
- **Admin Dashboard**: React + Material-UI
- **State Management**: Redux Toolkit + RTK Query
- **Build Tool**: Vite for fast development

### Backend
- **API Framework**: Node.js + Express + TypeScript
- **Database**: PostgreSQL 14+ with PostGIS
- **Cache**: Redis for sessions and real-time data
- **File Storage**: AWS S3 or MinIO
- **Message Queue**: Bull Queue with Redis

### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Orchestration**: Kubernetes (production)
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana

## Phase 1: Project Setup & Core Infrastructure (Week 1-2)

### 1.1 Initialize Project Structure
```bash
# Create project root
mkdir loan-collection-system
cd loan-collection-system

# Create directory structure
mkdir -p {backend,frontend,mobile-pwa,database,docs,scripts,docker}

# Initialize backend
cd backend
npm init -y
npm install express typescript @types/node @types/express
npm install -D nodemon ts-node

# Initialize mobile PWA
cd ../mobile-pwa
npx create-react-app . --template typescript
npm install @reduxjs/toolkit react-redux workbox-webpack-plugin
```

### 1.2 Setup Database
```bash
# Start PostgreSQL with Docker
cd ../docker
cat > docker-compose.yml << EOF
version: '3.8'
services:
  postgres:
    image: postgis/postgis:14-3.2
    environment:
      POSTGRES_DB: loan_collection
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database:/docker-entrypoint-initdb.d
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
EOF

# Start services
docker-compose up -d

# Run database schema
psql -h localhost -U admin -d loan_collection -f ../database/schema.sql
```

### 1.3 Backend API Foundation
```typescript
// backend/src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/agents', require('./routes/agents'));
app.use('/api/leads', require('./routes/leads'));
app.use('/api/payments', require('./routes/payments'));

export default app;
```

## Phase 2: Authentication & Agent Management (Week 3)

### 2.1 JWT Authentication System
```typescript
// backend/src/middleware/auth.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

interface AuthRequest extends Request {
  agent?: any;
}

export const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, agent) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.agent = agent;
    next();
  });
};
```

### 2.2 Agent Registration & Profile Management
```typescript
// backend/src/routes/agents.ts
import express from 'express';
import bcrypt from 'bcrypt';
import { Pool } from 'pg';

const router = express.Router();
const db = new Pool({ connectionString: process.env.DATABASE_URL });

router.post('/register', async (req, res) => {
  const { name, phone, email, password, employeeId } = req.body;
  
  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const result = await db.query(
      `INSERT INTO agents (name, phone, email, password_hash, employee_id) 
       VALUES ($1, $2, $3, $4, $5) RETURNING id, name, phone, email`,
      [name, phone, email, hashedPassword, employeeId]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    res.status(400).json({ error: 'Registration failed' });
  }
});

export default router;
```

## Phase 3: Mobile PWA Development (Week 4-5)

### 3.1 PWA Configuration
```typescript
// mobile-pwa/src/serviceWorkerRegistration.ts
const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
  window.location.hostname === '[::1]' ||
  window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)
);

export function register(config?: Config) {
  if ('serviceWorker' in navigator) {
    const publicUrl = new URL(process.env.PUBLIC_URL!, window.location.href);
    if (publicUrl.origin !== window.location.origin) {
      return;
    }

    window.addEventListener('load', () => {
      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;

      if (isLocalhost) {
        checkValidServiceWorker(swUrl, config);
      } else {
        registerValidSW(swUrl, config);
      }
    });
  }
}
```

### 3.2 Offline Data Management
```typescript
// mobile-pwa/src/utils/offlineStorage.ts
import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface OfflineDB extends DBSchema {
  leads: {
    key: string;
    value: Lead;
    indexes: { 'by-status': string };
  };
  collections: {
    key: string;
    value: Collection;
    indexes: { 'by-timestamp': Date };
  };
  sync_queue: {
    key: string;
    value: SyncItem;
  };
}

class OfflineStorageManager {
  private db: IDBPDatabase<OfflineDB> | null = null;

  async init() {
    this.db = await openDB<OfflineDB>('loan-collection', 1, {
      upgrade(db) {
        const leadsStore = db.createObjectStore('leads', { keyPath: 'id' });
        leadsStore.createIndex('by-status', 'status');

        const collectionsStore = db.createObjectStore('collections', { keyPath: 'id' });
        collectionsStore.createIndex('by-timestamp', 'timestamp');

        db.createObjectStore('sync_queue', { keyPath: 'id' });
      },
    });
  }

  async saveLeads(leads: Lead[]) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('leads', 'readwrite');
    await Promise.all(leads.map(lead => tx.store.put(lead)));
  }

  async getLeadsByStatus(status: string): Promise<Lead[]> {
    if (!this.db) await this.init();
    return this.db!.getAllFromIndex('leads', 'by-status', status);
  }
}

export const offlineStorage = new OfflineStorageManager();
```

### 3.3 Location Tracking
```typescript
// mobile-pwa/src/hooks/useLocation.ts
import { useState, useEffect } from 'react';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export const useLocation = () => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported');
      return;
    }

    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        });
      },
      (error) => {
        setError(error.message);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );

    return () => navigator.geolocation.clearWatch(watchId);
  }, []);

  return { location, error };
};
```

## Phase 4: Payment Integration (Week 6)

### 4.1 Payment Gateway Integration
```typescript
// backend/src/services/paymentService.ts
import Razorpay from 'razorpay';

class PaymentService {
  private razorpay: Razorpay;

  constructor() {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID!,
      key_secret: process.env.RAZORPAY_KEY_SECRET!,
    });
  }

  async createUPIPayment(amount: number, customerPhone: string) {
    const options = {
      amount: amount * 100, // Convert to paise
      currency: 'INR',
      method: {
        upi: true,
      },
      customer: {
        contact: customerPhone,
      },
      notes: {
        purpose: 'loan_collection',
      },
    };

    return await this.razorpay.orders.create(options);
  }

  async verifyPayment(paymentId: string, orderId: string, signature: string) {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(`${orderId}|${paymentId}`)
      .digest('hex');

    return expectedSignature === signature;
  }
}

export const paymentService = new PaymentService();
```

### 4.2 Receipt Generation
```typescript
// backend/src/services/receiptService.ts
import PDFDocument from 'pdfkit';
import AWS from 'aws-sdk';

class ReceiptService {
  private s3: AWS.S3;

  constructor() {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION,
    });
  }

  async generateReceipt(paymentData: PaymentData): Promise<string> {
    const doc = new PDFDocument();
    
    // Add company header
    doc.fontSize(20).text('Loan Collection Receipt', 50, 50);
    
    // Add payment details
    doc.fontSize(12)
       .text(`Receipt No: ${paymentData.receiptNumber}`, 50, 100)
       .text(`Date: ${new Date().toLocaleDateString()}`, 50, 120)
       .text(`Customer: ${paymentData.customerName}`, 50, 140)
       .text(`Amount: ₹${paymentData.amount}`, 50, 160)
       .text(`Payment Method: ${paymentData.method}`, 50, 180);

    // Convert to buffer
    const chunks: Buffer[] = [];
    doc.on('data', chunk => chunks.push(chunk));
    
    return new Promise((resolve, reject) => {
      doc.on('end', async () => {
        const pdfBuffer = Buffer.concat(chunks);
        
        // Upload to S3
        const uploadParams = {
          Bucket: process.env.S3_BUCKET_NAME!,
          Key: `receipts/${paymentData.receiptNumber}.pdf`,
          Body: pdfBuffer,
          ContentType: 'application/pdf',
        };

        try {
          const result = await this.s3.upload(uploadParams).promise();
          resolve(result.Location);
        } catch (error) {
          reject(error);
        }
      });
      
      doc.end();
    });
  }
}

export const receiptService = new ReceiptService();
```

## Phase 5: Lead Distribution Algorithm (Week 7)

### 5.1 Intelligent Assignment
```typescript
// backend/src/services/leadDistributionService.ts
class LeadDistributionService {
  async assignLeads(leads: Lead[], agents: Agent[]): Promise<Assignment[]> {
    const assignments: Assignment[] = [];

    for (const lead of leads) {
      const scores = await this.calculateAgentScores(lead, agents);
      const bestAgent = scores.reduce((best, current) => 
        current.score > best.score ? current : best
      );

      assignments.push({
        leadId: lead.id,
        agentId: bestAgent.agentId,
        score: bestAgent.score,
        assignedAt: new Date(),
      });
    }

    return assignments;
  }

  private async calculateAgentScores(lead: Lead, agents: Agent[]): Promise<AgentScore[]> {
    const scores: AgentScore[] = [];

    for (const agent of agents) {
      const proximityScore = this.calculateProximityScore(lead, agent);
      const workloadScore = this.calculateWorkloadScore(agent);
      const performanceScore = this.calculatePerformanceScore(agent);

      const totalScore = (
        proximityScore * 0.4 +
        workloadScore * 0.3 +
        performanceScore * 0.3
      );

      scores.push({
        agentId: agent.id,
        score: totalScore,
        factors: {
          proximity: proximityScore,
          workload: workloadScore,
          performance: performanceScore,
        },
      });
    }

    return scores;
  }

  private calculateProximityScore(lead: Lead, agent: Agent): number {
    const distance = this.calculateDistance(
      lead.customer.coordinates,
      agent.currentLocation
    );
    
    // Score decreases with distance (max 10km for full score)
    return Math.max(0, 1 - (distance / 10000));
  }

  private calculateWorkloadScore(agent: Agent): number {
    const maxLeads = 20;
    const currentLeads = agent.activeLeads || 0;
    
    // Score decreases as workload increases
    return Math.max(0, 1 - (currentLeads / maxLeads));
  }

  private calculatePerformanceScore(agent: Agent): number {
    return (agent.successRate || 0) / 100;
  }
}
```

## Phase 6: Testing & Deployment (Week 8)

### 6.1 Testing Setup
```typescript
// backend/tests/auth.test.ts
import request from 'supertest';
import app from '../src/app';

describe('Authentication', () => {
  test('should register new agent', async () => {
    const response = await request(app)
      .post('/api/agents/register')
      .send({
        name: 'Test Agent',
        phone: '+91-9876543210',
        email: '<EMAIL>',
        password: 'password123',
        employeeId: 'EMP001',
      });

    expect(response.status).toBe(201);
    expect(response.body).toHaveProperty('id');
  });

  test('should login with valid credentials', async () => {
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        phone: '+91-9876543210',
        password: 'password123',
      });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('access_token');
  });
});
```

### 6.2 Production Deployment
```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loan-collection-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: loan-collection-api
  template:
    metadata:
      labels:
        app: loan-collection-api
    spec:
      containers:
      - name: api
        image: loan-collection/api:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
```

## Next Steps

1. **Performance Optimization**: Implement caching, database optimization
2. **Security Hardening**: Add rate limiting, input validation, security headers
3. **Monitoring**: Set up logging, metrics, and alerting
4. **Scaling**: Implement horizontal scaling and load balancing
5. **Advanced Features**: AI-powered insights, predictive analytics

This implementation guide provides a solid foundation for building a comprehensive loan collection system that rivals commercial solutions like FieldEZ.
